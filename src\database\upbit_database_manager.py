"""
Upbit数据库管理器
专门处理Upbit交易所的数据存储和查询
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from loguru import logger
from .exchange_database_manager import ExchangeDatabaseManager, ExchangeData, ListingEvent


class UpbitDatabaseManager(ExchangeDatabaseManager):
    """Upbit数据库管理器"""
    
    def __init__(self, db_path: str = "upbit_data.db"):
        super().__init__(db_path)
        self.exchange_name = "upbit"
    
    def save_upbit_listing(self, symbol: str, announcement_time: datetime, 
                          announcement_content: str, source: str = "upbit_api"):
        """保存Upbit上币公告"""
        event = ListingEvent(
            exchange=self.exchange_name,
            symbol=symbol,
            announcement_time=announcement_time,
            announcement_content=announcement_content,
            source=source
        )
        self.save_listing_event(event)
    
    def save_upbit_price_data(self, symbol: str, price: float, volume: float, 
                             timestamp: Optional[datetime] = None, **kwargs):
        """保存Upbit价格数据"""
        if timestamp is None:
            timestamp = datetime.now()
            
        data = ExchangeData(
            exchange=self.exchange_name,
            symbol=symbol,
            timestamp=timestamp,
            price=price,
            volume=volume,
            market_cap=kwargs.get('market_cap'),
            change_24h=kwargs.get('change_24h'),
            listing_type=kwargs.get('listing_type'),
            announcement_time=kwargs.get('announcement_time')
        )
        self.save_exchange_data(data, kwargs.get('raw_data'))
    
    def get_upbit_listings(self, processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取Upbit上币事件"""
        return self.get_listing_events(self.exchange_name, processed)
    
    def get_upbit_price_history(self, symbol: str, hours: int = 24) -> List[ExchangeData]:
        """获取Upbit价格历史"""
        end_time = datetime.now()
        start_time = end_time.replace(hour=end_time.hour - hours)
        return self.get_historical_data(self.exchange_name, symbol, start_time, end_time)
    
    def analyze_upbit_listing_performance(self, symbol: str) -> Dict[str, Any]:
        """分析Upbit上币表现"""
        try:
            # 获取上币事件
            events = [e for e in self.get_upbit_listings() if e.symbol == symbol]
            if not events:
                return {"error": "未找到上币事件"}
            
            event = events[0]  # 最新的事件
            
            # 获取价格历史 - Upbit通常需要更长时间观察
            price_history = self.get_upbit_price_history(symbol, 96)  # 4天数据
            
            if not price_history:
                return {"error": "未找到价格数据"}
            
            # 计算表现指标
            prices = [p.price for p in price_history]
            initial_price = prices[0]
            max_price = max(prices)
            current_price = prices[-1]
            
            max_gain = ((max_price - initial_price) / initial_price) * 100
            current_gain = ((current_price - initial_price) / initial_price) * 100
            
            # Upbit特有的分析 - 韩国市场FOMO效应
            peak_time = self._find_peak_time(price_history)
            fomo_intensity = self._calculate_fomo_intensity(prices)
            
            return {
                "symbol": symbol,
                "exchange": self.exchange_name,
                "announcement_time": event.announcement_time.isoformat(),
                "initial_price": initial_price,
                "max_price": max_price,
                "current_price": current_price,
                "max_gain_percent": max_gain,
                "current_gain_percent": current_gain,
                "peak_time_hours": peak_time,
                "fomo_intensity": fomo_intensity,
                "data_points": len(price_history),
                "analysis_time": datetime.now().isoformat(),
                "exchange_characteristics": {
                    "typical_gain_range": "50-500%+",
                    "duration": "4-24 hours",
                    "volatility": "very high",
                    "fomo_factor": "extreme"
                }
            }
            
        except Exception as e:
            logger.error(f"分析Upbit上币表现失败: {e}")
            return {"error": str(e)}
    
    def _find_peak_time(self, price_history: List[ExchangeData]) -> float:
        """找到价格峰值时间"""
        if not price_history:
            return 0.0
        
        max_price = max(p.price for p in price_history)
        peak_data = next(p for p in price_history if p.price == max_price)
        
        # 计算从第一个数据点到峰值的时间差（小时）
        time_diff = peak_data.timestamp - price_history[0].timestamp
        return time_diff.total_seconds() / 3600
    
    def _calculate_fomo_intensity(self, prices: List[float]) -> str:
        """计算FOMO强度"""
        if len(prices) < 2:
            return "unknown"
        
        initial_price = prices[0]
        max_price = max(prices)
        max_gain = ((max_price - initial_price) / initial_price) * 100
        
        if max_gain > 300:
            return "extreme"
        elif max_gain > 150:
            return "high"
        elif max_gain > 50:
            return "moderate"
        else:
            return "low"
    
    def get_upbit_market_sentiment(self) -> Dict[str, Any]:
        """获取Upbit市场情绪分析"""
        try:
            # 获取最近的上币事件
            recent_events = self.get_upbit_listings()[:10]  # 最近10个事件
            
            if not recent_events:
                return {"error": "暂无数据"}
            
            # 分析平均表现
            total_gains = []
            for event in recent_events:
                analysis = self.analyze_upbit_listing_performance(event.symbol)
                if "max_gain_percent" in analysis:
                    total_gains.append(analysis["max_gain_percent"])
            
            if total_gains:
                avg_gain = sum(total_gains) / len(total_gains)
                max_gain = max(total_gains)
                min_gain = min(total_gains)
                
                return {
                    "exchange": self.exchange_name,
                    "recent_events_count": len(recent_events),
                    "analyzed_events": len(total_gains),
                    "average_gain_percent": avg_gain,
                    "max_gain_percent": max_gain,
                    "min_gain_percent": min_gain,
                    "market_sentiment": self._determine_market_sentiment(avg_gain),
                    "analysis_time": datetime.now().isoformat()
                }
            else:
                return {"error": "无法分析市场情绪"}
                
        except Exception as e:
            logger.error(f"获取Upbit市场情绪失败: {e}")
            return {"error": str(e)}
    
    def _determine_market_sentiment(self, avg_gain: float) -> str:
        """判断市场情绪"""
        if avg_gain > 200:
            return "extremely_bullish"
        elif avg_gain > 100:
            return "bullish"
        elif avg_gain > 50:
            return "positive"
        elif avg_gain > 0:
            return "neutral"
        else:
            return "bearish"


# 全局实例
upbit_db = UpbitDatabaseManager()
