#!/bin/bash

# 智能交易机器人 Docker 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 检查必要文件
check_files() {
    log_step "检查必要文件..."
    
    REQUIRED_FILES=(
        "config.yaml"
        ".env"
        "Dockerfile"
        "docker-compose.yml"
    )
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_info "文件检查完成"
}

# 创建.env文件模板
create_env_template() {
    if [[ ! -f ".env" ]]; then
        log_step "创建.env文件模板..."
        cat > .env << EOF
# Binance API配置
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here

# Telegram配置
TELEGRAM_API_ID=26145597
TELEGRAM_API_HASH=859206f58db62ec957089a7e9ff11d38
TELEGRAM_PHONE=

# 其他配置
TZ=Asia/Shanghai
LOG_LEVEL=INFO
EOF
        log_warn "请编辑.env文件，填入正确的API密钥"
        return 1
    fi
    return 0
}

# 构建镜像
build_image() {
    log_step "构建Docker镜像..."
    docker-compose build --no-cache
    log_info "镜像构建完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    docker-compose up -d
    log_info "服务启动完成"
}

# 显示状态
show_status() {
    log_step "服务状态:"
    docker-compose ps
    
    echo ""
    log_info "Web界面: http://localhost:8080"
    log_info "查看日志: docker-compose logs -f goldbot"
    log_info "停止服务: docker-compose down"
}

# 主函数
main() {
    echo "🐳 智能交易机器人 Docker 启动脚本"
    echo "=================================="
    
    # 检查环境
    check_docker
    
    # 检查文件
    check_files
    
    # 创建.env文件
    if ! create_env_template; then
        log_warn "请先配置.env文件后重新运行"
        exit 1
    fi
    
    # 构建镜像
    build_image
    
    # 启动服务
    start_services
    
    # 显示状态
    show_status
    
    echo ""
    log_info "🎉 智能交易机器人已在Docker中启动！"
}

# 处理命令行参数
case "${1:-}" in
    "build")
        check_docker
        build_image
        ;;
    "start")
        check_docker
        start_services
        show_status
        ;;
    "stop")
        docker-compose down
        log_info "服务已停止"
        ;;
    "restart")
        docker-compose restart
        show_status
        ;;
    "logs")
        docker-compose logs -f goldbot
        ;;
    "status")
        show_status
        ;;
    "clean")
        docker-compose down -v
        docker system prune -f
        log_info "清理完成"
        ;;
    *)
        main
        ;;
esac
