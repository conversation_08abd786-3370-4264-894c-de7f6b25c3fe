"""
交易所数据库管理器
管理各交易所的历史数据存储和查询
支持: Binance, Coinbase, Upbit, Bithumb
"""
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from loguru import logger
from dataclasses import dataclass, asdict


@dataclass
class ExchangeData:
    """交易所数据结构"""
    exchange: str
    symbol: str
    timestamp: datetime
    price: float
    volume: float
    market_cap: Optional[float] = None
    change_24h: Optional[float] = None
    listing_type: Optional[str] = None  # 上币类型
    announcement_time: Optional[datetime] = None  # 公告时间


@dataclass
class ListingEvent:
    """上币事件数据结构"""
    exchange: str
    symbol: str
    announcement_time: datetime
    announcement_content: Optional[str] = None
    marketcap: Optional[str] = None
    source: Optional[str] = None
    processed: bool = False


class ExchangeDatabaseManager:
    """交易所数据库管理器"""
    
    def __init__(self, db_path: str = "exchange_data.db"):
        self.db_path = db_path
        self.init_database()
        self.migrate_database()
        
    def init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建交易所数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS exchange_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    price REAL NOT NULL,
                    volume REAL NOT NULL,
                    market_cap REAL,
                    change_24h REAL,
                    listing_type TEXT,
                    announcement_time TEXT,
                    raw_data TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,

                    UNIQUE(exchange, symbol, timestamp)
                )
            ''')

            # 创建上币事件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS listing_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    announcement_time TEXT NOT NULL,
                    listing_time TEXT,
                    announcement_content TEXT,
                    marketcap TEXT,
                    source TEXT,
                    processed BOOLEAN DEFAULT FALSE,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,

                    UNIQUE(exchange, symbol, announcement_time)
                )
            ''')

            # 固定策略不需要复杂的价格分析表
            # price_performance表由price_performance_analyzer.py管理

            # 创建索引 - 分别处理每个索引，避免单个错误影响整体
            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_exchange_symbol_time
                    ON exchange_data(exchange, symbol, timestamp)
                ''')
                logger.debug("创建exchange_data索引成功")
            except Exception as e:
                logger.warning(f"创建exchange_data索引失败: {e}")

            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_timestamp
                    ON exchange_data(timestamp)
                ''')
                logger.debug("创建timestamp索引成功")
            except Exception as e:
                logger.warning(f"创建timestamp索引失败: {e}")

            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_listing_events
                    ON listing_events(exchange, symbol, announcement_time)
                ''')
                logger.debug("创建listing_events索引成功")
            except Exception as e:
                logger.warning(f"创建listing_events索引失败: {e}")

            # price_performance索引由price_performance_analyzer.py管理

            conn.commit()
            conn.close()

            logger.info("交易所数据库初始化完成")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            # 尝试关闭连接
            try:
                if 'conn' in locals():
                    conn.close()
            except:
                pass

    def migrate_database(self):
        """数据库迁移 - 处理重复数据和约束"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查是否需要迁移
            cursor.execute("PRAGMA table_info(listing_events)")
            columns = [row[1] for row in cursor.fetchall()]

            # 检查是否已有UNIQUE约束
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='listing_events'")
            table_sql = cursor.fetchone()

            if table_sql and 'UNIQUE(exchange, symbol, announcement_time)' not in table_sql[0]:
                logger.info("检测到旧版数据库，开始迁移...")

                # 创建临时表
                cursor.execute('''
                    CREATE TABLE listing_events_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        exchange TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        announcement_time TEXT NOT NULL,
                        listing_time TEXT,
                        announcement_content TEXT,
                        marketcap TEXT,
                        source TEXT,
                        processed BOOLEAN DEFAULT FALSE,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(exchange, symbol, announcement_time)
                    )
                ''')

                # 复制去重后的数据，添加marketcap字段
                cursor.execute('''
                    INSERT OR IGNORE INTO listing_events_new
                    (exchange, symbol, announcement_time, listing_time,
                     announcement_content, marketcap, source, processed, created_at)
                    SELECT DISTINCT exchange, symbol, announcement_time, listing_time,
                           announcement_content,
                           CASE WHEN EXISTS(SELECT 1 FROM pragma_table_info('listing_events') WHERE name='marketcap')
                                THEN marketcap ELSE NULL END as marketcap,
                           source, processed, created_at
                    FROM listing_events
                    ORDER BY created_at DESC
                ''')

                # 删除旧表
                cursor.execute('DROP TABLE listing_events')

                # 重命名新表
                cursor.execute('ALTER TABLE listing_events_new RENAME TO listing_events')

                # 重建索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_listing_events
                    ON listing_events(exchange, symbol, announcement_time)
                ''')

                conn.commit()
                logger.info("数据库迁移完成，重复数据已清理")

            # 清理超过90天的旧数据（可选）
            self._cleanup_old_listing_data(cursor, days=90)

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")

    def _cleanup_old_listing_data(self, cursor, days: int = 90):
        """清理超过指定天数的旧上币数据"""
        try:
            from datetime import datetime, timedelta

            cutoff_date = datetime.now() - timedelta(days=days)
            cutoff_str = cutoff_date.isoformat()

            # 删除超过90天的上币事件
            cursor.execute('''
                DELETE FROM listing_events
                WHERE announcement_time < ?
            ''', (cutoff_str,))

            deleted_count = cursor.rowcount
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 条超过{days}天的旧数据")

        except Exception as e:
            logger.warning(f"清理旧数据失败: {e}")
    
    def save_exchange_data(self, data: ExchangeData, raw_data: Optional[Dict] = None):
        """保存交易所数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO exchange_data
                (exchange, symbol, timestamp, price, volume, market_cap, change_24h,
                 listing_type, announcement_time, raw_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data.exchange,
                data.symbol,
                data.timestamp.isoformat(),
                data.price,
                data.volume,
                data.market_cap,
                data.change_24h,
                data.listing_type,
                data.announcement_time.isoformat() if data.announcement_time else None,
                json.dumps(raw_data) if raw_data else None
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"保存交易所数据失败: {e}")

    def save_listing_event(self, event: ListingEvent):
        """保存上币事件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR IGNORE INTO listing_events
                (exchange, symbol, announcement_time,
                 announcement_content, marketcap, source, processed)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                event.exchange,
                event.symbol,
                event.announcement_time.isoformat(),
                event.announcement_content,
                getattr(event, 'marketcap', None),
                event.source,
                event.processed
            ))

            # 检查是否插入成功
            if cursor.rowcount > 0:
                logger.debug(f"保存新上币事件: {event.exchange}-{event.symbol}")
            else:
                logger.debug(f"上币事件已存在，跳过: {event.exchange}-{event.symbol}")

            conn.commit()
            conn.close()

            logger.info(f"保存上币事件: {event.exchange} - {event.symbol}")

        except Exception as e:
            logger.error(f"保存上币事件失败: {e}")

    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 总记录数
            cursor.execute("SELECT COUNT(*) FROM listing_events")
            total_count = cursor.fetchone()[0]

            # 按交易所统计
            cursor.execute('''
                SELECT exchange, COUNT(*)
                FROM listing_events
                GROUP BY exchange
            ''')
            exchange_stats = dict(cursor.fetchall())

            # 最早和最新记录时间
            cursor.execute('''
                SELECT MIN(announcement_time), MAX(announcement_time)
                FROM listing_events
            ''')
            time_range = cursor.fetchone()

            # 最近30天的记录数
            from datetime import datetime, timedelta
            cutoff_30d = (datetime.now() - timedelta(days=30)).isoformat()
            cursor.execute('''
                SELECT COUNT(*) FROM listing_events
                WHERE announcement_time >= ?
            ''', (cutoff_30d,))
            recent_count = cursor.fetchone()[0]

            conn.close()

            return {
                'total_listings': total_count,
                'exchanges': exchange_stats,
                'time_range': {
                    'earliest': time_range[0],
                    'latest': time_range[1]
                },
                'recent_30d': recent_count
            }

        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {
                'total_listings': 0,
                'exchanges': {},
                'time_range': {'earliest': None, 'latest': None},
                'recent_30d': 0
            }

    def get_listing_events(self, exchange: Optional[str] = None,
                          processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取上币事件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = "SELECT exchange, symbol, announcement_time, announcement_content, marketcap, source, processed FROM listing_events"
            params = []
            conditions = []

            if exchange:
                conditions.append("exchange = ?")
                params.append(exchange)

            if processed is not None:
                conditions.append("processed = ?")
                params.append(processed)

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY announcement_time DESC"

            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()

            return [
                ListingEvent(
                    exchange=row[0],
                    symbol=row[1],
                    announcement_time=datetime.fromisoformat(row[2]),
                    announcement_content=row[3],
                    marketcap=row[4],
                    source=row[5],
                    processed=bool(row[6])
                )
                for row in rows
            ]

        except Exception as e:
            logger.error(f"获取上币事件失败: {e}")
            return []
    
    def get_latest_data(self, exchange: str, symbol: str) -> Optional[ExchangeData]:
        """获取最新数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT exchange, symbol, timestamp, price, volume, market_cap, change_24h
                FROM exchange_data 
                WHERE exchange = ? AND symbol = ?
                ORDER BY timestamp DESC 
                LIMIT 1
            ''', (exchange, symbol))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return ExchangeData(
                    exchange=row[0],
                    symbol=row[1],
                    timestamp=datetime.fromisoformat(row[2]),
                    price=row[3],
                    volume=row[4],
                    market_cap=row[5],
                    change_24h=row[6]
                )
            
            return None
            
        except Exception as e:
            logger.error(f"获取最新数据失败: {e}")
            return None
    
    def get_historical_data(self, exchange: str, symbol: str, 
                          start_time: datetime, end_time: datetime) -> List[ExchangeData]:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT exchange, symbol, timestamp, price, volume, market_cap, change_24h
                FROM exchange_data 
                WHERE exchange = ? AND symbol = ? 
                AND timestamp BETWEEN ? AND ?
                ORDER BY timestamp ASC
            ''', (exchange, symbol, start_time.isoformat(), end_time.isoformat()))
            
            rows = cursor.fetchall()
            conn.close()
            
            return [
                ExchangeData(
                    exchange=row[0],
                    symbol=row[1],
                    timestamp=datetime.fromisoformat(row[2]),
                    price=row[3],
                    volume=row[4],
                    market_cap=row[5],
                    change_24h=row[6]
                )
                for row in rows
            ]
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return []
    
    def get_exchange_symbols(self, exchange: str) -> List[str]:
        """获取交易所的所有交易对"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT DISTINCT symbol 
                FROM exchange_data 
                WHERE exchange = ?
                ORDER BY symbol
            ''', (exchange,))
            
            rows = cursor.fetchall()
            conn.close()
            
            return [row[0] for row in rows]
            
        except Exception as e:
            logger.error(f"获取交易对列表失败: {e}")
            return []
    
    def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                DELETE FROM exchange_data 
                WHERE timestamp < ?
            ''', (cutoff_time.isoformat(),))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            logger.info(f"清理了 {deleted_count} 条 {days} 天前的数据")
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
    
    def get_exchange_data(self, exchange: str, limit: int = 100) -> List[ExchangeData]:
        """获取交易所数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT exchange, symbol, timestamp, price, volume, market_cap, change_24h,
                       listing_type, announcement_time
                FROM exchange_data
                WHERE exchange = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (exchange, limit))

            rows = cursor.fetchall()
            conn.close()

            return [
                ExchangeData(
                    exchange=row[0],
                    symbol=row[1],
                    timestamp=datetime.fromisoformat(row[2]),
                    price=row[3],
                    volume=row[4],
                    market_cap=row[5],
                    change_24h=row[6],
                    listing_type=row[7],
                    announcement_time=datetime.fromisoformat(row[8]) if row[8] else None
                )
                for row in rows
            ]

        except Exception as e:
            logger.error(f"获取交易所数据失败: {e}")
            return []

    def get_all_exchanges(self) -> List[str]:
        """获取所有交易所列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT DISTINCT exchange
                FROM exchange_data
                ORDER BY exchange
            ''')

            rows = cursor.fetchall()
            conn.close()

            return [row[0] for row in rows]

        except Exception as e:
            logger.error(f"获取交易所列表失败: {e}")
            return []

    def get_recent_listings(self, exchange: Optional[str] = None, days: int = 7) -> List[ListingEvent]:
        """获取最近的上币事件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cutoff_time = datetime.now() - timedelta(days=days)

            if exchange:
                cursor.execute('''
                    SELECT exchange, symbol, announcement_time,
                           announcement_content, marketcap, source, processed
                    FROM listing_events
                    WHERE exchange = ? AND announcement_time >= ?
                    ORDER BY announcement_time DESC
                ''', (exchange, cutoff_time.isoformat()))
            else:
                cursor.execute('''
                    SELECT exchange, symbol, announcement_time,
                           announcement_content, marketcap, source, processed
                    FROM listing_events
                    WHERE announcement_time >= ?
                    ORDER BY announcement_time DESC
                ''', (cutoff_time.isoformat(),))

            rows = cursor.fetchall()
            conn.close()

            return [
                ListingEvent(
                    exchange=row[0],
                    symbol=row[1],
                    announcement_time=datetime.fromisoformat(row[2]),
                    announcement_content=row[3],
                    marketcap=row[4],
                    source=row[5],
                    processed=bool(row[6])
                )
                for row in rows
            ]

        except Exception as e:
            logger.error(f"获取最近上币事件失败: {e}")
            return []

    def get_statistics(self, exchange: str) -> Dict[str, Any]:
        """获取交易所统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 基础统计
            cursor.execute('''
                SELECT
                    COUNT(*) as total_records,
                    COUNT(DISTINCT symbol) as unique_symbols,
                    MIN(timestamp) as earliest_data,
                    MAX(timestamp) as latest_data
                FROM exchange_data
                WHERE exchange = ?
            ''', (exchange,))

            row = cursor.fetchone()

            if row and row[0] > 0:
                stats = {
                    'exchange': exchange,
                    'total_records': row[0],
                    'unique_symbols': row[1],
                    'earliest_data': row[2],
                    'latest_data': row[3],
                    'data_span_days': 0
                }

                # 计算数据跨度
                if row[2] and row[3]:
                    earliest = datetime.fromisoformat(row[2])
                    latest = datetime.fromisoformat(row[3])
                    stats['data_span_days'] = (latest - earliest).days

                conn.close()
                return stats
            else:
                conn.close()
                return {
                    'exchange': exchange,
                    'total_records': 0,
                    'unique_symbols': 0,
                    'message': '暂无数据'
                }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {'exchange': exchange, 'error': str(e)}


# 全局实例
exchange_db = ExchangeDatabaseManager()
