# 固定策略交易机器人配置文件
# 所有交易所使用相同的固定策略，无差异化处理
# 固定策略配置 - 所有交易所使用相同策略
fixed_strategy:
  enabled: true
  amount: 100  # 固定金额 100 USDT
  leverage: 10  # 10倍杠杆
  position_mode: isolated  # 逐仓模式
  first_close_time: 56  # 56秒后平仓80%
  first_close_percentage: 80  # 平仓80%
  monitor_duration: 300  # 5分钟监控期
  drawdown_threshold: 20  # 20%回撤阈值
logging:
  backup_count: 5
  file: trading_bot.log
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}'
  level: INFO
  max_size: 10MB
  retention: 30 days
  rotation: 1 day
# Telegram监控配置
monitoring:
  telegram:
    api_id: 26145597
    api_hash: "859206f58db62ec957089a7e9ff11d38"
    phone: "+8613375386798"
    channels:
      - "@BWEnews"
      - "@binance_announcements"
    # 精确的上币关键词 - 按交易所分类
    keywords:
      # Upbit关键词
      - "UPBIT LISTING:"
      - "UPBIT LISTING"
      - "Upbit 上新:"
      - "Upbit 上新: [交易]"
      - "[거래]"
      - "KRW 마켓"
      # Binance关键词
      - "Binance Will Add"
      # Coinbase关键词
      - "COINBASE LISTING:"
      - "COINBASE LISTING"
      - "COINBASE 上新"
      # Bithumb关键词
      - "Bithumb Listing:"
      - "Bithumb Listing"
      - "Bithumb상장:"
      - "Bithumb上新:"
      - "[마켓 추가]"
      - "마켓 추가"
      - "빗썸"
    # 排除关键词
    exclude_keywords:
      - "维护"
      - "暂停"
      - "停止"
      - "maintenance"
      - "suspend"
      - "delisting"
      - "下架"
      - "delist"
      - "暂停交易"
      - "停止交易"
      - "Token Swap"
      - "token swap"
      - "Rebranding"
      - "rebranding"
      - "Token Swap and Rebranding"

notifications:
  email:
    enabled: false
    password: ''
    smtp_port: 587
    smtp_server: ''
    to_emails: []
    username: ''
  feishu:
    enabled: true
    mention_all: false
    webhook_sign: ''
    webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/f38e8064-fbed-401e-96b1-ff5d7df44bbb
  telegram_bot:
    chat_id: ''
    enabled: false
    token: ''
# 币安交易配置
trading:
  binance:
    api_key: "vaiGFrmItkZ0iZqHOjHXFxrGk5Pk21oWblhh3rZZMTjR0f5zGAzbyO8Cc1N6Lmc0"
    api_secret: "Lm2ED1ahqFBKeKRAFRzUjkqL7jcDRDVhZv56Cfgi1qw8S7mpn351hEznjXASml5R"
    testnet: false  # 使用真实交易
    base_url: "https://fapi.binance.com"
    recv_window: 5000
    timeout: 30
    enabled: true

# 风险控制配置 - 固定策略专用
risk_control:
  signal_cooldown: 300  # 信号冷却时间 5分钟
  max_errors: 10  # 最大错误次数
  error_window: 300  # 错误时间窗口 5分钟
  blacklist: []  # 黑名单代币

# 统计配置
statistics:
  enabled: true
  cleanup_interval: 3600  # 每小时清理一次
  max_history_days: 7  # 保留7天历史

# 系统配置
system:
  auto_restart: true
  backup_config: true
  health_check_interval: 60
  max_memory_mb: 1024
  timezone: "Asia/Shanghai"
# Web界面配置
web:
  cors_origins:
  - '*'
  debug: false
  host: 0.0.0.0
  max_connections: 100
  port: 8081
  session_timeout: 3600
