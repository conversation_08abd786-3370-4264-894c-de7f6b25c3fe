"""
Coinbase数据库管理器
专门处理Coinbase交易所的数据存储和查询
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from loguru import logger
from .exchange_database_manager import ExchangeDatabaseManager, ExchangeData, ListingEvent


class CoinbaseDatabaseManager(ExchangeDatabaseManager):
    """Coinbase数据库管理器"""
    
    def __init__(self, db_path: str = "coinbase_data.db"):
        super().__init__(db_path)
        self.exchange_name = "coinbase"
    
    def save_coinbase_listing(self, symbol: str, announcement_time: datetime, 
                            announcement_content: str, source: str = "coinbase_api"):
        """保存Coinbase上币公告"""
        event = ListingEvent(
            exchange=self.exchange_name,
            symbol=symbol,
            announcement_time=announcement_time,
            announcement_content=announcement_content,
            source=source
        )
        self.save_listing_event(event)
    
    def save_coinbase_price_data(self, symbol: str, price: float, volume: float, 
                               timestamp: Optional[datetime] = None, **kwargs):
        """保存Coinbase价格数据"""
        if timestamp is None:
            timestamp = datetime.now()
            
        data = ExchangeData(
            exchange=self.exchange_name,
            symbol=symbol,
            timestamp=timestamp,
            price=price,
            volume=volume,
            market_cap=kwargs.get('market_cap'),
            change_24h=kwargs.get('change_24h'),
            listing_type=kwargs.get('listing_type'),
            announcement_time=kwargs.get('announcement_time')
        )
        self.save_exchange_data(data, kwargs.get('raw_data'))
    
    def get_coinbase_listings(self, processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取Coinbase上币事件"""
        return self.get_listing_events(self.exchange_name, processed)
    
    def get_coinbase_price_history(self, symbol: str, hours: int = 24) -> List[ExchangeData]:
        """获取Coinbase价格历史"""
        end_time = datetime.now()
        start_time = end_time.replace(hour=end_time.hour - hours)
        return self.get_historical_data(self.exchange_name, symbol, start_time, end_time)
    
    def analyze_coinbase_listing_performance(self, symbol: str) -> Dict[str, Any]:
        """分析Coinbase上币表现"""
        try:
            # 获取上币事件
            events = [e for e in self.get_coinbase_listings() if e.symbol == symbol]
            if not events:
                return {"error": "未找到上币事件"}
            
            event = events[0]  # 最新的事件
            
            # 获取价格历史
            price_history = self.get_coinbase_price_history(symbol, 48)  # 2天数据
            
            if not price_history:
                return {"error": "未找到价格数据"}
            
            # 计算表现指标
            prices = [p.price for p in price_history]
            initial_price = prices[0]
            max_price = max(prices)
            current_price = prices[-1]
            
            max_gain = ((max_price - initial_price) / initial_price) * 100
            current_gain = ((current_price - initial_price) / initial_price) * 100
            
            # Coinbase特有的分析 - 通常涨幅较小但稳定
            volatility = self._calculate_volatility(prices)
            
            return {
                "symbol": symbol,
                "exchange": self.exchange_name,
                "announcement_time": event.announcement_time.isoformat(),
                "initial_price": initial_price,
                "max_price": max_price,
                "current_price": current_price,
                "max_gain_percent": max_gain,
                "current_gain_percent": current_gain,
                "volatility": volatility,
                "data_points": len(price_history),
                "analysis_time": datetime.now().isoformat(),
                "exchange_characteristics": {
                    "typical_gain_range": "10-50%",
                    "duration": "1-4 hours",
                    "volatility": "low"
                }
            }
            
        except Exception as e:
            logger.error(f"分析Coinbase上币表现失败: {e}")
            return {"error": str(e)}
    
    def _calculate_volatility(self, prices: List[float]) -> float:
        """计算价格波动率"""
        if len(prices) < 2:
            return 0.0
        
        # 计算价格变化率的标准差
        changes = []
        for i in range(1, len(prices)):
            change = (prices[i] - prices[i-1]) / prices[i-1]
            changes.append(change)
        
        if not changes:
            return 0.0
        
        mean_change = sum(changes) / len(changes)
        variance = sum((x - mean_change) ** 2 for x in changes) / len(changes)
        return variance ** 0.5


# 全局实例
coinbase_db = CoinbaseDatabaseManager()
