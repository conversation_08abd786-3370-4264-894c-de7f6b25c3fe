"""
Bithumb数据库管理器
专门处理Bithumb交易所的数据存储和查询
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from loguru import logger
from .exchange_database_manager import ExchangeDatabaseManager, ExchangeData, ListingEvent


class BithumbDatabaseManager(ExchangeDatabaseManager):
    """Bithumb数据库管理器"""
    
    def __init__(self, db_path: str = "bithumb_data.db"):
        super().__init__(db_path)
        self.exchange_name = "bithumb"
    
    def save_bithumb_listing(self, symbol: str, announcement_time: datetime, 
                           announcement_content: str, source: str = "bithumb_api"):
        """保存Bithumb上币公告"""
        event = ListingEvent(
            exchange=self.exchange_name,
            symbol=symbol,
            announcement_time=announcement_time,
            announcement_content=announcement_content,
            source=source
        )
        self.save_listing_event(event)
    
    def save_bithumb_price_data(self, symbol: str, price: float, volume: float, 
                              timestamp: Optional[datetime] = None, **kwargs):
        """保存Bithumb价格数据"""
        if timestamp is None:
            timestamp = datetime.now()
            
        data = ExchangeData(
            exchange=self.exchange_name,
            symbol=symbol,
            timestamp=timestamp,
            price=price,
            volume=volume,
            market_cap=kwargs.get('market_cap'),
            change_24h=kwargs.get('change_24h'),
            listing_type=kwargs.get('listing_type'),
            announcement_time=kwargs.get('announcement_time')
        )
        self.save_exchange_data(data, kwargs.get('raw_data'))
    
    def get_bithumb_listings(self, processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取Bithumb上币事件"""
        return self.get_listing_events(self.exchange_name, processed)
    
    def get_bithumb_price_history(self, symbol: str, hours: int = 24) -> List[ExchangeData]:
        """获取Bithumb价格历史"""
        end_time = datetime.now()
        start_time = end_time.replace(hour=end_time.hour - hours)
        return self.get_historical_data(self.exchange_name, symbol, start_time, end_time)
    
    def analyze_bithumb_listing_performance(self, symbol: str) -> Dict[str, Any]:
        """分析Bithumb上币表现"""
        try:
            # 获取上币事件
            events = [e for e in self.get_bithumb_listings() if e.symbol == symbol]
            if not events:
                return {"error": "未找到上币事件"}
            
            event = events[0]  # 最新的事件
            
            # 获取价格历史
            price_history = self.get_bithumb_price_history(symbol, 48)  # 2天数据
            
            if not price_history:
                return {"error": "未找到价格数据"}
            
            # 计算表现指标
            prices = [p.price for p in price_history]
            initial_price = prices[0]
            max_price = max(prices)
            current_price = prices[-1]
            
            max_gain = ((max_price - initial_price) / initial_price) * 100
            current_gain = ((current_price - initial_price) / initial_price) * 100
            
            # Bithumb特有的分析 - 韩国本土交易所特性
            korean_premium = self._calculate_korean_premium(prices)
            stability_score = self._calculate_stability_score(prices)
            
            return {
                "symbol": symbol,
                "exchange": self.exchange_name,
                "announcement_time": event.announcement_time.isoformat(),
                "initial_price": initial_price,
                "max_price": max_price,
                "current_price": current_price,
                "max_gain_percent": max_gain,
                "current_gain_percent": current_gain,
                "korean_premium": korean_premium,
                "stability_score": stability_score,
                "data_points": len(price_history),
                "analysis_time": datetime.now().isoformat(),
                "exchange_characteristics": {
                    "typical_gain_range": "30-150%",
                    "duration": "2-8 hours",
                    "volatility": "moderate-high",
                    "korean_factor": "moderate"
                }
            }
            
        except Exception as e:
            logger.error(f"分析Bithumb上币表现失败: {e}")
            return {"error": str(e)}
    
    def _calculate_korean_premium(self, prices: List[float]) -> float:
        """计算韩国溢价（相对于国际价格的溢价）"""
        # 这里简化处理，实际应该对比国际交易所价格
        if len(prices) < 2:
            return 0.0
        
        # 假设初始价格为国际价格，计算平均溢价
        base_price = prices[0]
        premiums = [(p - base_price) / base_price * 100 for p in prices[1:]]
        
        return sum(premiums) / len(premiums) if premiums else 0.0
    
    def _calculate_stability_score(self, prices: List[float]) -> float:
        """计算价格稳定性评分 (0-100)"""
        if len(prices) < 3:
            return 50.0  # 默认中等稳定性
        
        # 计算价格变化的标准差
        mean_price = sum(prices) / len(prices)
        variance = sum((p - mean_price) ** 2 for p in prices) / len(prices)
        std_dev = variance ** 0.5
        
        # 计算相对标准差（变异系数）
        cv = std_dev / mean_price if mean_price > 0 else 1.0
        
        # 转换为稳定性评分 (变异系数越小，稳定性越高)
        stability = max(0, min(100, 100 - (cv * 100)))
        
        return round(stability, 2)
    
    def get_bithumb_korean_market_analysis(self) -> Dict[str, Any]:
        """获取Bithumb韩国市场分析"""
        try:
            # 获取最近的上币事件
            recent_events = self.get_bithumb_listings()[:15]  # 最近15个事件
            
            if not recent_events:
                return {"error": "暂无数据"}
            
            # 分析韩国市场特性
            analyses = []
            for event in recent_events:
                analysis = self.analyze_bithumb_listing_performance(event.symbol)
                if "korean_premium" in analysis:
                    analyses.append(analysis)
            
            if analyses:
                avg_gain = sum(a["max_gain_percent"] for a in analyses) / len(analyses)
                avg_premium = sum(a["korean_premium"] for a in analyses) / len(analyses)
                avg_stability = sum(a["stability_score"] for a in analyses) / len(analyses)
                
                return {
                    "exchange": self.exchange_name,
                    "recent_events_count": len(recent_events),
                    "analyzed_events": len(analyses),
                    "average_gain_percent": avg_gain,
                    "average_korean_premium": avg_premium,
                    "average_stability_score": avg_stability,
                    "market_characteristics": {
                        "korean_premium_trend": self._analyze_premium_trend(avg_premium),
                        "stability_level": self._analyze_stability_level(avg_stability),
                        "recommendation": self._generate_recommendation(avg_gain, avg_premium, avg_stability)
                    },
                    "analysis_time": datetime.now().isoformat()
                }
            else:
                return {"error": "无法分析韩国市场特性"}
                
        except Exception as e:
            logger.error(f"获取Bithumb韩国市场分析失败: {e}")
            return {"error": str(e)}
    
    def _analyze_premium_trend(self, avg_premium: float) -> str:
        """分析溢价趋势"""
        if avg_premium > 20:
            return "high_premium"
        elif avg_premium > 10:
            return "moderate_premium"
        elif avg_premium > 0:
            return "low_premium"
        else:
            return "discount"
    
    def _analyze_stability_level(self, avg_stability: float) -> str:
        """分析稳定性水平"""
        if avg_stability > 80:
            return "very_stable"
        elif avg_stability > 60:
            return "stable"
        elif avg_stability > 40:
            return "moderate"
        else:
            return "volatile"
    
    def _generate_recommendation(self, avg_gain: float, avg_premium: float, avg_stability: float) -> str:
        """生成交易建议"""
        if avg_gain > 100 and avg_stability > 60:
            return "strong_buy"
        elif avg_gain > 50 and avg_stability > 40:
            return "buy"
        elif avg_gain > 20:
            return "moderate_buy"
        elif avg_gain > 0:
            return "hold"
        else:
            return "avoid"


# 全局实例
bithumb_db = BithumbDatabaseManager()
