"""
配置管理器
支持运行时动态修改配置参数，无需重启系统
"""
import asyncio
import json
import yaml
import os
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from loguru import logger


class ConfigChangeHandler(FileSystemEventHandler):
    """配置文件变化处理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith(('.yaml', '.yml', '.json')):
            logger.info(f"检测到配置文件变化: {event.src_path}")
            asyncio.create_task(self.config_manager.reload_config())


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = config_file
        self.config: Dict[str, Any] = {}
        self.config_backup: Dict[str, Any] = {}
        self.last_modified = None
        
        # 配置变化回调
        self.change_callbacks: List[Callable] = []
        
        # 文件监控
        self.observer = Observer()
        self.file_handler = ConfigChangeHandler(self)
        
        # 热重载配置
        self.hot_reload_enabled = True
        self.auto_backup = True
        self.max_backups = 10
        
        # 配置验证规则
        self.validation_rules = {
            'trading.position.amount': {'type': (int, float), 'min': 1, 'max': 10000},
            'trading.position.leverage': {'type': int, 'min': 1, 'max': 20},
            'risk_control.max_positions': {'type': int, 'min': 1, 'max': 20},
            'risk_control.max_exposure': {'type': (int, float), 'min': 100, 'max': 100000},
            'notifications.feishu.enabled': {'type': bool},
            'web.port': {'type': int, 'min': 1000, 'max': 65535}
        }
        
        logger.info("配置管理器初始化完成")
        
    async def initialize(self):
        """初始化配置管理器"""
        try:
            # 加载初始配置
            await self.load_config()
            
            # 启动文件监控
            if self.hot_reload_enabled:
                self.start_file_monitoring()
                
            logger.info("配置管理器初始化完成")
            
        except Exception as e:
            logger.error(f"配置管理器初始化异常: {e}")
            raise
            
    async def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_file):
                logger.error(f"配置文件不存在: {self.config_file}")
                return {}
                
            # 检查文件修改时间
            current_modified = os.path.getmtime(self.config_file)
            if self.last_modified and current_modified == self.last_modified:
                return self.config
                
            # 备份当前配置
            if self.config and self.auto_backup:
                self.config_backup = self.config.copy()
                
            # 加载新配置
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.endswith('.json'):
                    new_config = json.load(f)
                else:
                    new_config = yaml.safe_load(f)
                    
            # 验证配置
            validation_result = self.validate_config(new_config)
            if not validation_result['valid']:
                logger.error(f"配置验证失败: {validation_result['errors']}")
                if self.config_backup:
                    logger.info("恢复备份配置")
                    self.config = self.config_backup
                    return self.config
                else:
                    raise ValueError(f"配置验证失败: {validation_result['errors']}")
                    
            self.config = new_config
            self.last_modified = current_modified
            
            logger.info("配置加载成功")
            return self.config
            
        except Exception as e:
            logger.error(f"加载配置异常: {e}")
            if self.config_backup:
                logger.info("恢复备份配置")
                self.config = self.config_backup
            return self.config
            
    async def reload_config(self):
        """重新加载配置"""
        try:
            old_config = self.config.copy()
            new_config = await self.load_config()
            
            if new_config != old_config:
                logger.info("配置已更新，通知组件")
                
                # 计算配置差异
                changes = self.calculate_config_diff(old_config, new_config)
                
                # 通知所有回调
                for callback in self.change_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(new_config, changes)
                        else:
                            callback(new_config, changes)
                    except Exception as e:
                        logger.error(f"配置变化回调异常: {e}")
                        
                # 保存配置变化日志
                self.log_config_change(changes)
                
        except Exception as e:
            logger.error(f"重新加载配置异常: {e}")
            
    def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证配置"""
        errors = []
        warnings = []
        
        try:
            for key_path, rules in self.validation_rules.items():
                value = self.get_nested_value(config, key_path)
                
                if value is None:
                    warnings.append(f"配置项 {key_path} 未设置")
                    continue
                    
                # 类型检查
                if 'type' in rules:
                    expected_types = rules['type']
                    if not isinstance(expected_types, tuple):
                        expected_types = (expected_types,)
                    if not isinstance(value, expected_types):
                        errors.append(f"配置项 {key_path} 类型错误，期望 {expected_types}，实际 {type(value)}")
                        continue
                        
                # 范围检查
                if isinstance(value, (int, float)):
                    if 'min' in rules and value < rules['min']:
                        errors.append(f"配置项 {key_path} 值过小，最小值 {rules['min']}，实际 {value}")
                    if 'max' in rules and value > rules['max']:
                        errors.append(f"配置项 {key_path} 值过大，最大值 {rules['max']}，实际 {value}")
                        
                # 自定义验证
                if 'validator' in rules:
                    try:
                        if not rules['validator'](value):
                            errors.append(f"配置项 {key_path} 自定义验证失败")
                    except Exception as e:
                        errors.append(f"配置项 {key_path} 验证异常: {e}")
                        
        except Exception as e:
            errors.append(f"配置验证异常: {e}")
            
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
        
    def get_nested_value(self, config: Dict[str, Any], key_path: str) -> Any:
        """获取嵌套配置值"""
        try:
            keys = key_path.split('.')
            value = config
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return None
            return value
        except Exception:
            return None
            
    def set_nested_value(self, config: Dict[str, Any], key_path: str, value: Any):
        """设置嵌套配置值"""
        try:
            keys = key_path.split('.')
            current = config
            
            # 导航到父级
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
                
            # 设置值
            current[keys[-1]] = value
            
        except Exception as e:
            logger.error(f"设置配置值异常: {e}")
            
    def calculate_config_diff(self, old_config: Dict[str, Any], new_config: Dict[str, Any]) -> Dict[str, Any]:
        """计算配置差异"""
        changes = {
            'added': [],
            'modified': [],
            'removed': []
        }
        
        try:
            # 递归比较配置
            self._compare_dict(old_config, new_config, changes, '')
            
        except Exception as e:
            logger.error(f"计算配置差异异常: {e}")
            
        return changes
        
    def _compare_dict(self, old_dict: Dict[str, Any], new_dict: Dict[str, Any], changes: Dict[str, List], prefix: str):
        """递归比较字典"""
        # 检查新增和修改
        for key, new_value in new_dict.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if key not in old_dict:
                changes['added'].append({'key': full_key, 'value': new_value})
            elif old_dict[key] != new_value:
                if isinstance(old_dict[key], dict) and isinstance(new_value, dict):
                    self._compare_dict(old_dict[key], new_value, changes, full_key)
                else:
                    changes['modified'].append({
                        'key': full_key,
                        'old_value': old_dict[key],
                        'new_value': new_value
                    })
                    
        # 检查删除
        for key, old_value in old_dict.items():
            full_key = f"{prefix}.{key}" if prefix else key
            if key not in new_dict:
                changes['removed'].append({'key': full_key, 'value': old_value})
                
    def log_config_change(self, changes: Dict[str, Any]):
        """记录配置变化"""
        try:
            if not any(changes.values()):
                return
                
            logger.info("配置变化详情:")
            
            for added in changes.get('added', []):
                logger.info(f"  + 新增: {added['key']} = {added['value']}")
                
            for modified in changes.get('modified', []):
                logger.info(f"  ~ 修改: {modified['key']} = {modified['old_value']} -> {modified['new_value']}")
                
            for removed in changes.get('removed', []):
                logger.info(f"  - 删除: {removed['key']} = {removed['value']}")
                
            # 保存变化日志到文件
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'changes': changes
            }
            
            log_file = 'config_changes.log'
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
                
        except Exception as e:
            logger.error(f"记录配置变化异常: {e}")
            
    def start_file_monitoring(self):
        """启动文件监控"""
        try:
            config_dir = os.path.dirname(os.path.abspath(self.config_file))
            self.observer.schedule(self.file_handler, config_dir, recursive=False)
            self.observer.start()
            logger.info(f"配置文件监控已启动: {config_dir}")
        except Exception as e:
            logger.error(f"启动文件监控异常: {e}")
            
    def stop_file_monitoring(self):
        """停止文件监控"""
        try:
            if self.observer.is_alive():
                self.observer.stop()
                self.observer.join()
            logger.info("配置文件监控已停止")
        except Exception as e:
            logger.error(f"停止文件监控异常: {e}")
            
    def add_change_callback(self, callback: Callable):
        """添加配置变化回调"""
        self.change_callbacks.append(callback)
        
    def remove_change_callback(self, callback: Callable):
        """移除配置变化回调"""
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)
            
    async def update_config(self, key_path: str, value: Any, save: bool = True) -> bool:
        """动态更新配置"""
        try:
            # 验证单个配置项
            if key_path in self.validation_rules:
                temp_config = {key_path.replace('.', '_'): value}
                validation_result = self.validate_config(temp_config)
                if not validation_result['valid']:
                    logger.error(f"配置更新验证失败: {validation_result['errors']}")
                    return False
                    
            # 更新配置
            old_value = self.get_nested_value(self.config, key_path)
            self.set_nested_value(self.config, key_path, value)
            
            # 保存到文件
            if save:
                await self.save_config()
                
            # 通知变化
            changes = {
                'modified': [{
                    'key': key_path,
                    'old_value': old_value,
                    'new_value': value
                }],
                'added': [],
                'removed': []
            }
            
            for callback in self.change_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(self.config, changes)
                    else:
                        callback(self.config, changes)
                except Exception as e:
                    logger.error(f"配置变化回调异常: {e}")
                    
            logger.info(f"配置更新成功: {key_path} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"更新配置异常: {e}")
            return False
            
    async def save_config(self):
        """保存配置到文件"""
        try:
            # 创建备份
            if self.auto_backup and os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                import shutil
                shutil.copy2(self.config_file, backup_file)
                
                # 清理旧备份
                self.cleanup_old_backups()
                
            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                if self.config_file.endswith('.json'):
                    json.dump(self.config, f, ensure_ascii=False, indent=2)
                else:
                    yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
                    
            logger.info("配置保存成功")
            
        except Exception as e:
            logger.error(f"保存配置异常: {e}")
            
    def cleanup_old_backups(self):
        """清理旧备份文件"""
        try:
            config_dir = os.path.dirname(os.path.abspath(self.config_file))
            config_name = os.path.basename(self.config_file)
            
            # 查找备份文件
            backup_files = []
            for file in os.listdir(config_dir):
                if file.startswith(f"{config_name}.backup."):
                    backup_files.append(os.path.join(config_dir, file))
                    
            # 按修改时间排序，保留最新的几个
            backup_files.sort(key=os.path.getmtime, reverse=True)
            
            for backup_file in backup_files[self.max_backups:]:
                os.remove(backup_file)
                logger.debug(f"删除旧备份文件: {backup_file}")
                
        except Exception as e:
            logger.error(f"清理备份文件异常: {e}")
            
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.config.copy()
        
    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """获取配置值"""
        value = self.get_nested_value(self.config, key_path)
        return value if value is not None else default
        
    async def shutdown(self):
        """关闭配置管理器"""
        try:
            self.stop_file_monitoring()
            logger.info("配置管理器已关闭")
        except Exception as e:
            logger.error(f"关闭配置管理器异常: {e}")
