#!/bin/bash
# GoldBot Ubuntu服务器一键部署脚本
# 使用方法: chmod +x deploy_ubuntu.sh && ./deploy_ubuntu.sh

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo ./deploy_ubuntu.sh"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_step "检查系统版本..."
    
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测系统版本"
        exit 1
    fi
    
    . /etc/os-release
    
    if [[ "$ID" != "ubuntu" ]]; then
        log_error "此脚本仅支持Ubuntu系统"
        exit 1
    fi
    
    log_info "检测到系统: $PRETTY_NAME"
}

# 更新系统
update_system() {
    log_step "更新系统包..."
    
    apt update
    apt upgrade -y
    
    log_info "系统更新完成"
}

# 安装基础工具
install_basic_tools() {
    log_step "安装基础工具..."
    
    apt install -y \
        curl \
        wget \
        git \
        vim \
        nano \
        htop \
        screen \
        unzip \
        zip \
        tree \
        net-tools \
        lsof \
        bc \
        fail2ban \
        ufw
    
    log_info "基础工具安装完成"
}

# 安装Python
install_python() {
    log_step "安装Python 3.9..."
    
    apt install -y \
        python3.9 \
        python3.9-venv \
        python3.9-dev \
        python3-pip \
        build-essential
    
    # 设置Python3.9为默认python3
    update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.9 1
    
    # 验证安装
    python3 --version
    
    log_info "Python安装完成"
}

# 创建项目目录
create_project_dir() {
    log_step "创建项目目录..."
    
    PROJECT_DIR="/opt/goldbot"
    
    if [[ -d "$PROJECT_DIR" ]]; then
        log_warn "项目目录已存在，创建备份..."
        mv "$PROJECT_DIR" "${PROJECT_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
    fi
    
    mkdir -p "$PROJECT_DIR"
    cd "$PROJECT_DIR"
    
    log_info "项目目录创建完成: $PROJECT_DIR"
}

# 检查项目文件
check_project_files() {
    log_step "检查项目文件..."
    
    REQUIRED_FILES=(
        "requirements.txt"
        "config.yaml"
        ".env"
        "start_bot.py"
        "main.py"
    )
    
    MISSING_FILES=()
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [[ ! -f "$file" ]]; then
            MISSING_FILES+=("$file")
        fi
    done
    
    if [[ ${#MISSING_FILES[@]} -gt 0 ]]; then
        log_error "缺少以下必要文件:"
        for file in "${MISSING_FILES[@]}"; do
            echo "  - $file"
        done
        log_info "请将项目文件上传到 $PROJECT_DIR 目录后重新运行脚本"
        exit 1
    fi
    
    log_info "项目文件检查完成"
}

# 创建Python虚拟环境
create_venv() {
    log_step "创建Python虚拟环境..."
    
    python3 -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    log_info "虚拟环境创建完成"
}

# 安装项目依赖
install_dependencies() {
    log_step "安装项目依赖..."
    
    source venv/bin/activate
    
    # 安装依赖，如果失败尝试使用国内镜像
    if ! pip install -r requirements.txt; then
        log_warn "使用默认源安装失败，尝试使用清华镜像源..."
        pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    fi
    
    log_info "项目依赖安装完成"
}

# 设置文件权限
set_permissions() {
    log_step "设置文件权限..."
    
    chown -R root:root /opt/goldbot
    chmod -R 755 /opt/goldbot
    
    # 设置脚本执行权限
    find /opt/goldbot -name "*.py" -exec chmod +x {} \;
    find /opt/goldbot -name "*.sh" -exec chmod +x {} \;
    
    log_info "文件权限设置完成"
}

# 创建systemd服务
create_systemd_service() {
    log_step "创建systemd服务..."
    
    cat > /etc/systemd/system/goldbot.service << 'EOF'
[Unit]
Description=GoldBot Trading System
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/goldbot
Environment=PATH=/opt/goldbot/venv/bin
ExecStart=/opt/goldbot/venv/bin/python /opt/goldbot/start_bot.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    systemctl daemon-reload
    
    # 启用服务（开机自启动）
    systemctl enable goldbot
    
    log_info "systemd服务创建完成"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    # 允许SSH端口
    ufw allow ssh
    
    # 允许Web界面端口
    ufw allow 8080
    
    # 启用防火墙
    ufw --force enable
    
    log_info "防火墙配置完成"
}

# 创建管理脚本
create_management_scripts() {
    log_step "创建管理脚本..."
    
    # 创建启动脚本
    cat > /opt/goldbot/start.sh << 'EOF'
#!/bin/bash
echo "启动GoldBot服务..."
systemctl start goldbot
systemctl status goldbot
EOF
    
    # 创建停止脚本
    cat > /opt/goldbot/stop.sh << 'EOF'
#!/bin/bash
echo "停止GoldBot服务..."
systemctl stop goldbot
systemctl status goldbot
EOF
    
    # 创建重启脚本
    cat > /opt/goldbot/restart.sh << 'EOF'
#!/bin/bash
echo "重启GoldBot服务..."
systemctl restart goldbot
systemctl status goldbot
EOF
    
    # 创建日志查看脚本
    cat > /opt/goldbot/logs.sh << 'EOF'
#!/bin/bash
echo "查看GoldBot实时日志..."
echo "按Ctrl+C退出日志查看"
journalctl -u goldbot -f
EOF
    
    # 创建状态检查脚本
    cat > /opt/goldbot/status.sh << 'EOF'
#!/bin/bash
echo "=== GoldBot系统状态 ==="
echo ""
echo "服务状态:"
systemctl status goldbot --no-pager
echo ""
echo "系统资源:"
echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "内存使用: $(free -h | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
echo "磁盘使用: $(df -h / | tail -1 | awk '{print $5}')"
echo ""
echo "网络端口:"
netstat -tulpn | grep :8080
EOF
    
    # 创建备份脚本
    cat > /opt/goldbot/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/goldbot_backup_$(date +%Y%m%d_%H%M%S)"
echo "创建备份目录: $BACKUP_DIR"
mkdir -p $BACKUP_DIR

echo "备份配置文件..."
cp /opt/goldbot/config.yaml $BACKUP_DIR/
cp /opt/goldbot/.env $BACKUP_DIR/

echo "备份数据文件..."
cp /opt/goldbot/*.json $BACKUP_DIR/ 2>/dev/null || true
cp /opt/goldbot/*.db $BACKUP_DIR/ 2>/dev/null || true

echo "备份日志文件..."
cp /opt/goldbot/*.log $BACKUP_DIR/ 2>/dev/null || true

echo "备份完成: $BACKUP_DIR"
ls -la $BACKUP_DIR
EOF
    
    # 设置执行权限
    chmod +x /opt/goldbot/*.sh
    
    log_info "管理脚本创建完成"
}

# 测试配置
test_configuration() {
    log_step "测试配置..."
    
    cd /opt/goldbot
    source venv/bin/activate
    
    # 测试Python导入
    if python -c "import sys; print('Python版本:', sys.version)"; then
        log_info "Python环境测试通过"
    else
        log_error "Python环境测试失败"
        exit 1
    fi
    
    # 测试配置文件
    if python -c "import yaml; yaml.safe_load(open('config.yaml'))"; then
        log_info "配置文件格式测试通过"
    else
        log_error "配置文件格式错误"
        exit 1
    fi
    
    log_info "配置测试完成"
}

# 显示部署结果
show_deployment_result() {
    log_step "部署完成！"
    
    echo ""
    echo "🎉 GoldBot部署成功！"
    echo ""
    echo "📋 重要信息:"
    echo "  项目目录: /opt/goldbot"
    echo "  配置文件: /opt/goldbot/config.yaml"
    echo "  环境变量: /opt/goldbot/.env"
    echo "  服务名称: goldbot"
    echo ""
    echo "🔧 管理命令:"
    echo "  启动服务: systemctl start goldbot"
    echo "  停止服务: systemctl stop goldbot"
    echo "  重启服务: systemctl restart goldbot"
    echo "  查看状态: systemctl status goldbot"
    echo "  查看日志: journalctl -u goldbot -f"
    echo ""
    echo "📱 快捷脚本:"
    echo "  启动: /opt/goldbot/start.sh"
    echo "  停止: /opt/goldbot/stop.sh"
    echo "  重启: /opt/goldbot/restart.sh"
    echo "  日志: /opt/goldbot/logs.sh"
    echo "  状态: /opt/goldbot/status.sh"
    echo "  备份: /opt/goldbot/backup.sh"
    echo ""
    echo "🌐 Web界面:"
    echo "  访问地址: http://$(curl -s ifconfig.me 2>/dev/null || echo "你的服务器IP"):8080"
    echo ""
    echo "⚠️ 下一步操作:"
    echo "1. 检查并修改配置文件: nano /opt/goldbot/config.yaml"
    echo "2. 检查并修改环境变量: nano /opt/goldbot/.env"
    echo "3. 启动服务: systemctl start goldbot"
    echo "4. 查看运行状态: systemctl status goldbot"
    echo "5. 查看实时日志: journalctl -u goldbot -f"
    echo ""
}

# 主函数
main() {
    echo "🚀 GoldBot Ubuntu服务器一键部署脚本"
    echo "========================================"
    echo ""
    
    check_root
    check_system
    update_system
    install_basic_tools
    install_python
    create_project_dir
    check_project_files
    create_venv
    install_dependencies
    set_permissions
    create_systemd_service
    configure_firewall
    create_management_scripts
    test_configuration
    show_deployment_result
    
    echo ""
    echo "✅ 部署脚本执行完成！"
}

# 执行主函数
main "$@"
