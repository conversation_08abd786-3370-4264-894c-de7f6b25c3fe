#!/usr/bin/env python3
"""
飞书通知测试脚本
测试飞书通知功能是否正常工作
"""
import asyncio
import sys
from datetime import datetime
from loguru import logger

from src.utils import load_config, load_env_config, setup_logger
from src.notifications.feishu_notifier import FeishuNotifier
from src.models import TradingSignal, SignalSource, TokenInfo, TradingOrder, OrderSide, OrderStatus


async def test_feishu_notifications():
    """测试飞书通知功能"""
    
    # 设置日志
    setup_logger(level="DEBUG")
    
    logger.info("=== 飞书通知测试 ===")
    
    try:
        # 加载配置
        config = load_config("config_with_feishu.yaml")
        env_config = load_env_config()
        
        # 合并飞书配置
        feishu_config = config.get('notifications', {}).get('feishu', {})
        feishu_config.update(env_config.get('feishu', {}))
        
        if not feishu_config.get('webhook_url'):
            logger.error("飞书Webhook URL未配置，请在config_with_feishu.yaml或.env中设置")
            return False
            
        # 初始化飞书通知器
        notifier = FeishuNotifier(feishu_config)
        
        if not notifier.enabled:
            logger.error("飞书通知器未启用")
            return False
            
        logger.info("飞书通知器初始化成功")
        
        # 测试1: 固定策略信号通知
        logger.info("测试1: 发送固定策略信号通知")
        test_signal_fixed = TradingSignal(
            source=SignalSource.TELEGRAM,
            symbol="BTC",
            timestamp=datetime.now(),
            content="Binance Will List BTC (Bitcoin) - Fixed Strategy Test",
            tokens=[TokenInfo(symbol="BTC", market_cap=1000000000)],
            confidence=0.9,
            priority=1,
            exchange="binance",
            metadata={
                'detected_exchange': 'binance',
                'signal_source': 'telegram',
                'market_cap': 1000000000
            }
        )

        result1 = await notifier.send_signal_notification(test_signal_upbit)
        logger.info(f"Upbit信号通知发送结果: {result1}")

        await asyncio.sleep(2)  # 等待2秒

        # 测试1.2: Binance上新币信号通知
        logger.info("测试1.2: 发送Binance上新币信号通知")
        test_signal_binance = TradingSignal(
            source=SignalSource.TELEGRAM,
            symbol="ETH",
            timestamp=datetime.now(),
            content="Binance listing announcement: ETH perpetual futures now available",
            tokens=[TokenInfo(symbol="ETH", market_cap=500000000)],
            confidence=0.8,
            priority=2,
            exchange="binance",
            metadata={
                'listing_exchange': 'binance',
                'signal_source': 'telegram',
                'market_cap': 500000000
            }
        )
        
        result1_2 = await notifier.send_signal_notification(test_signal_binance)
        logger.info(f"Binance信号通知发送结果: {result1_2}")

        await asyncio.sleep(2)  # 等待2秒
        
        # 测试2: 开仓通知
        logger.info("测试2: 发送开仓通知")
        test_order = TradingOrder(
            symbol="BTCUSDT",
            side=OrderSide.LONG,
            amount=100.0,
            leverage=10,
            stop_loss_percent=80,
            status=OrderStatus.FILLED,
            order_id="test_order_123",
            filled_price=45000.0,
            created_at=datetime.now(),
            filled_at=datetime.now()
        )
        
        result2 = await notifier.send_trade_notification(test_order, "开仓")
        logger.info(f"开仓通知发送结果: {result2}")
        
        await asyncio.sleep(2)  # 等待2秒
        
        # 测试3: 盈利通知
        logger.info("测试3: 发送盈利通知")
        current_price = 46500.0
        profit = (46500 - 45000) * 100 / 45000 * 10  # 计算盈利
        profit_percent = (46500 - 45000) / 45000 * 100 * 10
        
        result3 = await notifier.send_profit_notification(
            test_order, 
            current_price, 
            profit, 
            profit_percent
        )
        logger.info(f"盈利通知发送结果: {result3}")
        
        await asyncio.sleep(2)  # 等待2秒
        
        # 测试4: 统计通知
        logger.info("测试4: 发送统计通知")
        test_stats = {
            'total_trades': 25,
            'successful_trades': 18,
            'failed_trades': 7,
            'total_profit': 1250.50,
            'win_rate': 0.72
        }
        
        result4 = await notifier.send_statistics_notification(test_stats)
        logger.info(f"统计通知发送结果: {result4}")
        
        # 测试结果汇总
        all_success = all([result1, result1_2, result2, result3, result4])

        logger.info("=== 测试结果汇总 ===")
        logger.info(f"Upbit信号通知: {'✓' if result1 else '✗'}")
        logger.info(f"Binance信号通知: {'✓' if result1_2 else '✗'}")
        logger.info(f"开仓通知: {'✓' if result2 else '✗'}")
        logger.info(f"盈利通知: {'✓' if result3 else '✗'}")
        logger.info(f"统计通知: {'✓' if result4 else '✗'}")
        logger.info(f"总体结果: {'✓ 全部成功' if all_success else '✗ 部分失败'}")

        return all_success
        
    except Exception as e:
        logger.error(f"测试飞书通知异常: {e}")
        return False


def test_signal_levels():
    """测试信号等级分类"""
    logger.info("=== 测试上币交易所优先级分类 ===")

    try:
        # 创建通知器
        notifier = FeishuNotifier({'enabled': False})  # 不实际发送

        # 测试固定策略信号等级（所有交易所统一）
        test_exchanges = [
            ("binance", "Binance Will List BTC (Bitcoin)"),
            ("coinbase", "Coinbase Pro adds support for ETH"),
            ("bithumb", "Bithumb new listing: DOGE trading starts"),
            ("upbit", "Upbit Digital Asset Market Support (ADA)")
        ]

        for exchange, content in test_exchanges:
            signal = TradingSignal(
                source=SignalSource.TELEGRAM,
                symbol="TEST",
                timestamp=datetime.now(),
                content=content,
                tokens=[TokenInfo(symbol="TEST")],
                exchange=exchange
            )

            level_info = notifier.get_signal_level(signal)
            logger.info(f"{exchange.upper():10} -> {level_info['level']:15} {level_info['emoji']} {level_info['name']} (固定策略统一处理)")

        return True

    except Exception as e:
        logger.error(f"测试信号等级异常: {e}")
        return False


async def main():
    """主函数"""
    logger.info("开始飞书通知测试...")
    
    # 测试信号等级分类
    level_test_result = test_signal_levels()
    
    # 测试飞书通知功能
    notification_test_result = await test_feishu_notifications()
    
    logger.info("=== 最终测试结果 ===")
    logger.info(f"信号等级测试: {'✓' if level_test_result else '✗'}")
    logger.info(f"飞书通知测试: {'✓' if notification_test_result else '✗'}")
    
    if level_test_result and notification_test_result:
        logger.info("🎉 所有测试通过！飞书通知功能正常")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查配置")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试异常: {e}")
        sys.exit(1)
