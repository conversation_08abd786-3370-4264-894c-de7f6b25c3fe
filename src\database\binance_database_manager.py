"""
Binance数据库管理器
专门处理Binance交易所的数据存储和查询
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from loguru import logger
from .exchange_database_manager import ExchangeDatabaseManager, ExchangeData, ListingEvent


class BinanceDatabaseManager(ExchangeDatabaseManager):
    """Binance数据库管理器"""
    
    def __init__(self, db_path: str = "binance_data.db"):
        super().__init__(db_path)
        self.exchange_name = "binance"
    
    def save_binance_listing(self, symbol: str, announcement_time: datetime, 
                           announcement_content: str, source: str = "binance_api"):
        """保存Binance上币公告"""
        event = ListingEvent(
            exchange=self.exchange_name,
            symbol=symbol,
            announcement_time=announcement_time,
            announcement_content=announcement_content,
            source=source
        )
        self.save_listing_event(event)
    
    def save_binance_price_data(self, symbol: str, price: float, volume: float, 
                               timestamp: Optional[datetime] = None, **kwargs):
        """保存Binance价格数据"""
        if timestamp is None:
            timestamp = datetime.now()
            
        data = ExchangeData(
            exchange=self.exchange_name,
            symbol=symbol,
            timestamp=timestamp,
            price=price,
            volume=volume,
            market_cap=kwargs.get('market_cap'),
            change_24h=kwargs.get('change_24h'),
            listing_type=kwargs.get('listing_type'),
            announcement_time=kwargs.get('announcement_time')
        )
        self.save_exchange_data(data, kwargs.get('raw_data'))
    
    def get_binance_listings(self, processed: Optional[bool] = None) -> List[ListingEvent]:
        """获取Binance上币事件"""
        return self.get_listing_events(self.exchange_name, processed)
    
    def get_binance_price_history(self, symbol: str, hours: int = 24) -> List[ExchangeData]:
        """获取Binance价格历史"""
        end_time = datetime.now()
        start_time = end_time.replace(hour=end_time.hour - hours)
        return self.get_historical_data(self.exchange_name, symbol, start_time, end_time)
    
    def analyze_binance_listing_performance(self, symbol: str) -> Dict[str, Any]:
        """分析Binance上币表现"""
        try:
            # 获取上币事件
            events = [e for e in self.get_binance_listings() if e.symbol == symbol]
            if not events:
                return {"error": "未找到上币事件"}
            
            event = events[0]  # 最新的事件
            
            # 获取价格历史
            price_history = self.get_binance_price_history(symbol, 72)  # 3天数据
            
            if not price_history:
                return {"error": "未找到价格数据"}
            
            # 计算表现指标
            prices = [p.price for p in price_history]
            initial_price = prices[0]
            max_price = max(prices)
            current_price = prices[-1]
            
            max_gain = ((max_price - initial_price) / initial_price) * 100
            current_gain = ((current_price - initial_price) / initial_price) * 100
            
            return {
                "symbol": symbol,
                "exchange": self.exchange_name,
                "announcement_time": event.announcement_time.isoformat(),
                "initial_price": initial_price,
                "max_price": max_price,
                "current_price": current_price,
                "max_gain_percent": max_gain,
                "current_gain_percent": current_gain,
                "data_points": len(price_history),
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"分析Binance上币表现失败: {e}")
            return {"error": str(e)}


# 全局实例
binance_db = BinanceDatabaseManager()
