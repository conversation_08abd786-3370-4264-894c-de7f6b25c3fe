"""
历史数据分析系统
分析各交易所上币效果，为差异化策略提供数据支持
"""
import asyncio
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from loguru import logger
import aiohttp
import ccxt

from ..models import TradingSignal, SignalSource


@dataclass
class ListingAnalysis:
    """上币分析数据"""
    exchange: str
    symbol: str
    listing_date: datetime
    listing_price: float
    
    # 价格数据 (涨跌幅度 %)
    price_1m: float
    price_5m: float
    price_15m: float
    price_30m: float
    price_1h: float
    price_2h: float
    price_4h: float
    price_1d: float
    
    # 涨跌幅度
    change_1m: float
    change_5m: float
    change_15m: float
    change_30m: float
    change_1h: float
    change_2h: float
    change_4h: float
    change_1d: float
    
    # 回撤数据
    max_drawdown_1h: float
    max_drawdown_4h: float
    max_drawdown_1d: float
    
    # 达峰时间 (分钟)
    peak_time_minutes: int
    peak_price: float
    peak_change: float
    
    # 市值数据
    market_cap: Optional[float]
    volume_24h: Optional[float]
    
    # 分析时间
    analyzed_at: datetime


class HistoricalAnalyzer:
    """历史数据分析器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_path = config.get('analysis', {}).get('db_path', 'historical_analysis.db')
        
        # 初始化数据库
        self.init_database()
        
        # 交易所API (仅用于获取历史数据)
        # 注意：这些API仅用于获取历史数据，不用于交易
        try:
            self.exchanges = {
                'binance': ccxt.binance({'sandbox': False}),
                'coinbase': ccxt.coinbase({'sandbox': False})
            }
        except Exception as e:
            logger.warning(f"初始化交易所API异常: {e}")
            self.exchanges = {}
        
        logger.info("历史数据分析器初始化完成")
        
    def init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建上币分析表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS listing_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    listing_date TEXT NOT NULL,
                    listing_price REAL NOT NULL,
                    
                    price_1m REAL,
                    price_5m REAL,
                    price_15m REAL,
                    price_30m REAL,
                    price_1h REAL,
                    price_2h REAL,
                    price_4h REAL,
                    price_1d REAL,
                    
                    change_1m REAL,
                    change_5m REAL,
                    change_15m REAL,
                    change_30m REAL,
                    change_1h REAL,
                    change_2h REAL,
                    change_4h REAL,
                    change_1d REAL,
                    
                    max_drawdown_1h REAL,
                    max_drawdown_4h REAL,
                    max_drawdown_1d REAL,
                    
                    peak_time_minutes INTEGER,
                    peak_price REAL,
                    peak_change REAL,
                    
                    market_cap REAL,
                    volume_24h REAL,
                    
                    analyzed_at TEXT NOT NULL,
                    
                    UNIQUE(exchange, symbol, listing_date)
                )
            ''')
            
            # 创建Telegram信号记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS telegram_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    signal_date TEXT NOT NULL,
                    content TEXT NOT NULL,
                    processed BOOLEAN DEFAULT FALSE,
                    
                    UNIQUE(exchange, symbol, signal_date)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化异常: {e}")
            
    async def analyze_recent_listings(self, months: int = 3) -> Dict[str, Any]:
        """分析最近几个月的上币数据"""
        try:
            logger.info(f"开始分析最近{months}个月的上币数据...")
            
            # 从Telegram获取历史信号
            signals = await self.fetch_telegram_signals(months)
            
            # 分析每个信号
            results = {
                'upbit': [],
                'binance': [],
                'coinbase': [],
                'bithumb': []
            }
            
            for signal in signals:
                try:
                    analysis = await self.analyze_listing(signal)
                    if analysis:
                        results[signal['exchange']].append(analysis)
                        # 保存到数据库
                        self.save_analysis(analysis)
                        
                except Exception as e:
                    logger.error(f"分析信号异常: {signal} - {e}")
                    
            # 生成统计报告
            report = self.generate_analysis_report(results)
            
            logger.info("历史数据分析完成")
            return report
            
        except Exception as e:
            logger.error(f"分析历史数据异常: {e}")
            return {}
            
    async def fetch_telegram_signals(self, months: int) -> List[Dict[str, Any]]:
        """从Telegram获取历史信号 (模拟实现)"""
        try:
            # 这里应该连接到Telegram API获取历史消息
            # 由于复杂性，这里提供模拟数据结构
            
            signals = []
            
            # 模拟一些历史信号数据
            sample_signals = [
                {
                    'exchange': 'upbit',
                    'symbol': 'PEPE',
                    'listing_date': datetime.now() - timedelta(days=30),
                    'content': 'Upbit Market Support (PEPE)'
                },
                {
                    'exchange': 'binance',
                    'symbol': 'ARB',
                    'listing_date': datetime.now() - timedelta(days=45),
                    'content': 'Binance will list Arbitrum (ARB)'
                },
                {
                    'exchange': 'coinbase',
                    'symbol': 'MATIC',
                    'listing_date': datetime.now() - timedelta(days=60),
                    'content': 'Coinbase Pro listing: MATIC'
                }
            ]
            
            logger.info(f"获取到 {len(sample_signals)} 个历史信号")
            return sample_signals
            
        except Exception as e:
            logger.error(f"获取Telegram历史信号异常: {e}")
            return []
            
    async def analyze_listing(self, signal: Dict[str, Any]) -> Optional[ListingAnalysis]:
        """分析单个上币事件"""
        try:
            exchange = signal['exchange']
            symbol = signal['symbol']
            listing_date = signal['listing_date']
            
            logger.info(f"分析上币事件: {exchange} - {symbol}")
            
            # 获取历史价格数据
            price_data = await self.fetch_historical_prices(exchange, symbol, listing_date)
            if not price_data:
                logger.warning(f"无法获取价格数据: {symbol}")
                return None
                
            # 计算各时间点的涨跌幅
            listing_price = price_data.get('listing_price', 0)
            if listing_price <= 0:
                return None
                
            # 计算涨跌幅度
            changes = {}
            prices = {}
            timeframes = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '1d']
            
            for tf in timeframes:
                price = price_data.get(f'price_{tf}', listing_price)
                prices[tf] = price
                changes[tf] = ((price - listing_price) / listing_price) * 100 if listing_price > 0 else 0
                
            # 计算回撤
            drawdowns = self.calculate_drawdowns(price_data)
            
            # 计算达峰时间
            peak_info = self.calculate_peak_info(price_data)
            
            # 获取市值数据
            market_data = await self.fetch_market_data(symbol)
            
            analysis = ListingAnalysis(
                exchange=exchange,
                symbol=symbol,
                listing_date=listing_date,
                listing_price=listing_price,
                
                price_1m=prices.get('1m', 0),
                price_5m=prices.get('5m', 0),
                price_15m=prices.get('15m', 0),
                price_30m=prices.get('30m', 0),
                price_1h=prices.get('1h', 0),
                price_2h=prices.get('2h', 0),
                price_4h=prices.get('4h', 0),
                price_1d=prices.get('1d', 0),
                
                change_1m=changes.get('1m', 0),
                change_5m=changes.get('5m', 0),
                change_15m=changes.get('15m', 0),
                change_30m=changes.get('30m', 0),
                change_1h=changes.get('1h', 0),
                change_2h=changes.get('2h', 0),
                change_4h=changes.get('4h', 0),
                change_1d=changes.get('1d', 0),
                
                max_drawdown_1h=drawdowns.get('1h', 0),
                max_drawdown_4h=drawdowns.get('4h', 0),
                max_drawdown_1d=drawdowns.get('1d', 0),
                
                peak_time_minutes=peak_info.get('time_minutes', 0),
                peak_price=peak_info.get('price', 0),
                peak_change=peak_info.get('change', 0),
                
                market_cap=market_data.get('market_cap'),
                volume_24h=market_data.get('volume_24h'),
                
                analyzed_at=datetime.now()
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析上币事件异常: {e}")
            return None
            
    async def fetch_historical_prices(self, exchange: str, symbol: str, listing_date: datetime) -> Dict[str, float]:
        """获取历史价格数据"""
        try:
            # 这里应该调用相应交易所的API获取历史数据
            # 由于复杂性，这里提供模拟数据
            
            base_price = 1.0  # 模拟上币价格
            
            # 模拟价格变化 (基于不同交易所的历史表现)
            multipliers = {
                'upbit': [1.05, 1.15, 1.25, 1.35, 1.45, 1.40, 1.30, 1.20],  # 较强表现
                'binance': [1.03, 1.08, 1.12, 1.18, 1.22, 1.18, 1.15, 1.10],  # 中等表现
                'coinbase': [1.01, 1.03, 1.05, 1.08, 1.10, 1.08, 1.06, 1.04],  # 较弱表现
                'bithumb': [1.02, 1.05, 1.08, 1.12, 1.15, 1.12, 1.10, 1.08]   # 中等表现
            }
            
            exchange_multipliers = multipliers.get(exchange, multipliers['binance'])
            timeframes = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '1d']
            
            price_data = {'listing_price': base_price}
            
            for i, tf in enumerate(timeframes):
                if i < len(exchange_multipliers):
                    price_data[f'price_{tf}'] = base_price * exchange_multipliers[i]
                else:
                    price_data[f'price_{tf}'] = base_price
                    
            return price_data
            
        except Exception as e:
            logger.error(f"获取历史价格异常: {e}")
            return {}
            
    def calculate_drawdowns(self, price_data: Dict[str, float]) -> Dict[str, float]:
        """计算回撤数据"""
        try:
            # 简化的回撤计算
            listing_price = price_data.get('listing_price', 1.0)
            
            # 模拟回撤数据
            return {
                '1h': -5.2,   # 1小时内最大回撤5.2%
                '4h': -8.5,   # 4小时内最大回撤8.5%
                '1d': -12.3   # 1天内最大回撤12.3%
            }
            
        except Exception as e:
            logger.error(f"计算回撤异常: {e}")
            return {}
            
    def calculate_peak_info(self, price_data: Dict[str, float]) -> Dict[str, Any]:
        """计算达峰信息"""
        try:
            # 简化的达峰计算
            listing_price = price_data.get('listing_price', 1.0)
            
            # 模拟达峰数据
            peak_price = listing_price * 1.45  # 假设峰值是上币价格的1.45倍
            peak_change = 45.0  # 45%涨幅
            peak_time = 90  # 90分钟达峰
            
            return {
                'price': peak_price,
                'change': peak_change,
                'time_minutes': peak_time
            }
            
        except Exception as e:
            logger.error(f"计算达峰信息异常: {e}")
            return {}
            
    async def fetch_market_data(self, symbol: str) -> Dict[str, Any]:
        """获取市值数据"""
        try:
            # 这里应该调用CoinGecko或其他API获取市值数据
            # 模拟数据
            return {
                'market_cap': 1000000000,  # 10亿美元市值
                'volume_24h': 50000000     # 5000万美元24小时交易量
            }
            
        except Exception as e:
            logger.error(f"获取市值数据异常: {e}")
            return {}
            
    def save_analysis(self, analysis: ListingAnalysis):
        """保存分析结果到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 转换数据
            data = asdict(analysis)
            data['listing_date'] = analysis.listing_date.isoformat()
            data['analyzed_at'] = analysis.analyzed_at.isoformat()
            
            # 插入数据
            cursor.execute('''
                INSERT OR REPLACE INTO listing_analysis 
                (exchange, symbol, listing_date, listing_price,
                 price_1m, price_5m, price_15m, price_30m, price_1h, price_2h, price_4h, price_1d,
                 change_1m, change_5m, change_15m, change_30m, change_1h, change_2h, change_4h, change_1d,
                 max_drawdown_1h, max_drawdown_4h, max_drawdown_1d,
                 peak_time_minutes, peak_price, peak_change,
                 market_cap, volume_24h, analyzed_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['exchange'], data['symbol'], data['listing_date'], data['listing_price'],
                data['price_1m'], data['price_5m'], data['price_15m'], data['price_30m'],
                data['price_1h'], data['price_2h'], data['price_4h'], data['price_1d'],
                data['change_1m'], data['change_5m'], data['change_15m'], data['change_30m'],
                data['change_1h'], data['change_2h'], data['change_4h'], data['change_1d'],
                data['max_drawdown_1h'], data['max_drawdown_4h'], data['max_drawdown_1d'],
                data['peak_time_minutes'], data['peak_price'], data['peak_change'],
                data['market_cap'], data['volume_24h'], data['analyzed_at']
            ))
            
            conn.commit()
            conn.close()
            
            logger.debug(f"保存分析结果: {analysis.exchange} - {analysis.symbol}")
            
        except Exception as e:
            logger.error(f"保存分析结果异常: {e}")
            
    def generate_analysis_report(self, results: Dict[str, List[ListingAnalysis]]) -> Dict[str, Any]:
        """生成分析报告"""
        try:
            report = {
                'summary': {},
                'exchange_performance': {},
                'recommendations': {}
            }
            
            total_listings = sum(len(listings) for listings in results.values())
            report['summary']['total_listings'] = total_listings
            report['summary']['analysis_date'] = datetime.now().isoformat()
            
            # 分析各交易所表现
            for exchange, listings in results.items():
                if not listings:
                    continue
                    
                # 计算平均表现
                avg_1h = sum(l.change_1h for l in listings) / len(listings)
                avg_4h = sum(l.change_4h for l in listings) / len(listings)
                avg_1d = sum(l.change_1d for l in listings) / len(listings)
                avg_peak = sum(l.peak_change for l in listings) / len(listings)
                avg_peak_time = sum(l.peak_time_minutes for l in listings) / len(listings)
                
                report['exchange_performance'][exchange] = {
                    'total_listings': len(listings),
                    'avg_change_1h': avg_1h,
                    'avg_change_4h': avg_4h,
                    'avg_change_1d': avg_1d,
                    'avg_peak_change': avg_peak,
                    'avg_peak_time_minutes': avg_peak_time,
                    'success_rate_1h': len([l for l in listings if l.change_1h > 0]) / len(listings),
                    'success_rate_4h': len([l for l in listings if l.change_4h > 0]) / len(listings),
                    'success_rate_1d': len([l for l in listings if l.change_1d > 0]) / len(listings)
                }
                
            # 生成策略建议
            report['recommendations'] = self.generate_strategy_recommendations(report['exchange_performance'])
            
            return report
            
        except Exception as e:
            logger.error(f"生成分析报告异常: {e}")
            return {}
            
    def generate_strategy_recommendations(self, performance: Dict[str, Any]) -> Dict[str, Any]:
        """基于历史数据生成策略建议"""
        try:
            recommendations = {}
            
            # 按1小时表现排序
            sorted_exchanges = sorted(
                performance.items(),
                key=lambda x: x[1]['avg_change_1h'],
                reverse=True
            )
            
            for i, (exchange, perf) in enumerate(sorted_exchanges):
                rank = i + 1
                
                if rank == 1:
                    # 最佳表现 - 激进策略
                    recommendations[exchange] = {
                        'strategy': 'aggressive',
                        'position_multiplier': 1.5,
                        'leverage_multiplier': 1.2,
                        'take_profit_levels': 5,
                        'max_hold_hours': 48,
                        'reason': f'历史表现最佳，平均1小时涨幅{perf["avg_change_1h"]:.1f}%'
                    }
                elif rank == 2:
                    # 次佳表现 - 积极策略
                    recommendations[exchange] = {
                        'strategy': 'active',
                        'position_multiplier': 1.2,
                        'leverage_multiplier': 1.0,
                        'take_profit_levels': 4,
                        'max_hold_hours': 24,
                        'reason': f'历史表现良好，平均1小时涨幅{perf["avg_change_1h"]:.1f}%'
                    }
                else:
                    # 较差表现 - 保守策略
                    recommendations[exchange] = {
                        'strategy': 'conservative',
                        'position_multiplier': 0.8,
                        'leverage_multiplier': 0.8,
                        'take_profit_levels': 3,
                        'max_hold_hours': 12,
                        'reason': f'历史表现一般，平均1小时涨幅{perf["avg_change_1h"]:.1f}%'
                    }
                    
            return recommendations
            
        except Exception as e:
            logger.error(f"生成策略建议异常: {e}")
            return {}
            
    def get_exchange_statistics(self, exchange: str) -> Dict[str, Any]:
        """获取特定交易所的统计数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM listing_analysis WHERE exchange = ?
                ORDER BY listing_date DESC
            ''', (exchange,))
            
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                return {}
                
            # 计算统计数据
            total_count = len(rows)
            avg_1h = sum(row[9] for row in rows) / total_count  # change_1h
            avg_4h = sum(row[12] for row in rows) / total_count  # change_4h
            avg_1d = sum(row[13] for row in rows) / total_count  # change_1d
            
            return {
                'exchange': exchange,
                'total_listings': total_count,
                'avg_change_1h': avg_1h,
                'avg_change_4h': avg_4h,
                'avg_change_1d': avg_1d,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取交易所统计异常: {e}")
            return {}
