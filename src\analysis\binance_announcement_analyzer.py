#!/usr/bin/env python3
"""
Binance公告分析器 (简化版)
专门使用Binance API获取历史行情数据进行公告分析
只使用Binance，删除其他数据源
"""
import asyncio
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from loguru import logger
import json
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_sources.binance_historical_fetcher import BinanceHistoricalFetcher


class BinanceAnnouncementAnalyzer:
    """基于Binance数据的公告分析器"""
    
    def __init__(self):
        self.db_path = "binance_announcement_analysis.db"
        self.binance_fetcher = BinanceHistoricalFetcher()
        self.init_database()
        
    def init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建公告分析表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS binance_announcement_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    trading_pair TEXT NOT NULL,
                    announcement_time TEXT NOT NULL,
                    announcement_price REAL NOT NULL,
                    
                    -- 价格变化数据 (基于公告时间)
                    change_1min REAL, change_5min REAL, change_15min REAL, change_30min REAL,
                    change_1hour REAL, change_4hour REAL, change_12hour REAL, 
                    change_1day REAL, change_3day REAL,
                    
                    -- 关键指标
                    max_gain REAL,
                    max_loss REAL,
                    peak_time_hours REAL,
                    peak_price REAL,
                    max_drawdown_from_peak REAL,
                    volatility_3day REAL,
                    
                    -- 价格时间线 (JSON格式)
                    price_timeline TEXT,
                    
                    -- 元数据
                    data_source TEXT DEFAULT 'binance',
                    raw_klines_count INTEGER,
                    analyzed_at TEXT NOT NULL,
                    
                    UNIQUE(exchange, symbol, announcement_time)
                )
            ''')
            
            # 创建公告记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS announcement_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    announcement_time TEXT NOT NULL,
                    announcement_content TEXT NOT NULL,
                    source TEXT NOT NULL,  -- telegram/twitter/manual
                    processed BOOLEAN DEFAULT FALSE,
                    
                    UNIQUE(exchange, symbol, announcement_time)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Binance公告分析数据库初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            
    async def record_announcement(self, exchange: str, symbol: str, announcement_time: datetime, content: str, source: str = 'manual'):
        """记录公告信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO announcement_records 
                (exchange, symbol, announcement_time, announcement_content, source, processed)
                VALUES (?, ?, ?, ?, ?, FALSE)
            ''', (exchange, symbol, announcement_time.isoformat(), content, source))
            
            conn.commit()
            conn.close()
            
            logger.info(f"📝 记录公告: {exchange.upper()} {symbol} - {announcement_time}")
            
        except Exception as e:
            logger.error(f"❌ 记录公告失败: {e}")
            
    async def analyze_announcement(self, exchange: str, symbol: str, announcement_time: datetime) -> Optional[Dict]:
        """分析单个公告的价格影响"""
        logger.info(f"🔍 分析公告: {exchange.upper()} {symbol}")
        logger.info(f"📅 公告时间: {announcement_time}")
        
        try:
            # 使用Binance获取历史数据和分析
            result = await self.binance_fetcher.get_announcement_analysis(symbol, announcement_time)
            
            if not result:
                logger.error(f"❌ 无法获取 {symbol} 的Binance数据")
                return None
                
            # 保存分析结果到数据库
            await self.save_analysis_result(exchange, result)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 分析公告异常: {e}")
            return None
            
    async def save_analysis_result(self, exchange: str, result: Dict):
        """保存分析结果到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            analysis = result['analysis']
            changes = analysis['changes']
            
            cursor.execute('''
                INSERT OR REPLACE INTO binance_announcement_analysis 
                (exchange, symbol, trading_pair, announcement_time, announcement_price,
                 change_1min, change_5min, change_15min, change_30min, change_1hour, 
                 change_4hour, change_12hour, change_1day, change_3day,
                 max_gain, max_loss, peak_time_hours, peak_price, max_drawdown_from_peak, volatility_3day,
                 price_timeline, data_source, raw_klines_count, analyzed_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                exchange, result['symbol'], result['trading_pair'], result['announcement_time'], analysis['announcement_price'],
                changes.get('change_1min', 0), changes.get('change_5min', 0), changes.get('change_15min', 0), changes.get('change_30min', 0),
                changes.get('change_1hour', 0), changes.get('change_4hour', 0), changes.get('change_12hour', 0), 
                changes.get('change_1day', 0), changes.get('change_3day', 0),
                analysis['max_gain'], analysis['max_loss'], analysis['peak_time_hours'], analysis['peak_price'],
                analysis['max_drawdown_from_peak'], analysis['volatility_3day'],
                json.dumps(analysis['price_timeline']), result['data_source'], result['raw_klines_count'], datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"💾 保存分析结果: {result['symbol']}")
            
        except Exception as e:
            logger.error(f"❌ 保存分析结果失败: {e}")
            
    def get_exchange_statistics(self, exchange: str) -> Dict[str, Any]:
        """获取交易所统计数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_listings,
                    AVG(change_1hour) as avg_1hour_change,
                    AVG(max_gain) as avg_max_gain,
                    AVG(peak_time_hours) as avg_peak_time_hours,
                    AVG(max_drawdown_from_peak) as avg_max_drawdown,
                    AVG(volatility_3day) as avg_volatility,
                    COUNT(CASE WHEN change_1hour > 0 THEN 1 END) * 100.0 / COUNT(*) as success_rate
                FROM binance_announcement_analysis 
                WHERE exchange = ?
            ''', (exchange,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row and row[0] > 0:
                return {
                    'exchange': exchange,
                    'total_listings': int(row[0]),
                    'avg_1hour_change': round(row[1] or 0, 2),
                    'avg_max_gain': round(row[2] or 0, 2),
                    'avg_peak_time_hours': round(row[3] or 0, 2),
                    'avg_max_drawdown': round(row[4] or 0, 2),
                    'avg_volatility': round(row[5] or 0, 2),
                    'success_rate': round(row[6] or 0, 1)
                }
            else:
                return {
                    'exchange': exchange,
                    'total_listings': 0,
                    'message': '暂无分析数据'
                }
                
        except Exception as e:
            logger.error(f"❌ 获取统计数据失败: {e}")
            return {}
            
    def get_recent_analysis_data(self, exchange: str, limit: int = 10) -> List[Dict]:
        """获取最近的分析数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    symbol, trading_pair, announcement_time, announcement_price,
                    change_1min, change_5min, change_15min, change_30min, change_1hour,
                    change_4hour, change_12hour, change_1day, change_3day,
                    max_gain, max_loss, peak_time_hours, peak_price, max_drawdown_from_peak, volatility_3day,
                    price_timeline
                FROM binance_announcement_analysis 
                WHERE exchange = ?
                ORDER BY announcement_time DESC 
                LIMIT ?
            ''', (exchange, limit))
            
            rows = cursor.fetchall()
            conn.close()
            
            data_list = []
            for row in rows:
                try:
                    price_timeline = json.loads(row[19]) if row[19] else []
                except:
                    price_timeline = []
                    
                data_list.append({
                    'symbol': row[0],
                    'trading_pair': row[1],
                    'announcement_time': row[2],
                    'announcement_price': row[3],
                    'change_1min': row[4], 'change_5min': row[5], 'change_15min': row[6], 'change_30min': row[7],
                    'change_1hour': row[8], 'change_4hour': row[9], 'change_12hour': row[10], 
                    'change_1day': row[11], 'change_3day': row[12],
                    'max_gain': row[13], 'max_loss': row[14], 'peak_time_hours': row[15], 'peak_price': row[16],
                    'max_drawdown_from_peak': row[17], 'volatility_3day': row[18],
                    'price_timeline': price_timeline
                })
                
            return data_list
            
        except Exception as e:
            logger.error(f"❌ 获取分析数据失败: {e}")
            return []
            
    async def batch_analyze_announcements(self, announcements: List[Dict]):
        """批量分析公告"""
        logger.info(f"🔄 开始批量分析 {len(announcements)} 个公告...")
        
        results = []
        for i, announcement in enumerate(announcements, 1):
            logger.info(f"📊 分析进度: {i}/{len(announcements)}")
            
            try:
                result = await self.analyze_announcement(
                    announcement['exchange'],
                    announcement['symbol'], 
                    datetime.fromisoformat(announcement['announcement_time'])
                )
                
                if result:
                    results.append(result)
                    logger.info(f"✅ {announcement['symbol']} 分析完成")
                else:
                    logger.warning(f"⚠️ {announcement['symbol']} 分析失败")
                    
                # 避免API限制
                await asyncio.sleep(0.2)
                
            except Exception as e:
                logger.error(f"❌ 分析 {announcement['symbol']} 异常: {e}")
                
        logger.info(f"🎉 批量分析完成! 成功: {len(results)}/{len(announcements)}")
        return results
        
    def create_sample_data(self):
        """创建示例数据用于测试"""
        logger.info("🧪 创建示例公告数据...")
        
        # 示例公告数据 (使用一些历史时间点)
        sample_announcements = [
            {
                'exchange': 'upbit',
                'symbol': 'BTC',
                'announcement_time': (datetime.now() - timedelta(days=7)).isoformat(),
                'content': 'Upbit BTC 测试公告',
                'source': 'manual'
            },
            {
                'exchange': 'binance', 
                'symbol': 'ETH',
                'announcement_time': (datetime.now() - timedelta(days=5)).isoformat(),
                'content': 'Binance ETH 测试公告',
                'source': 'manual'
            },
            {
                'exchange': 'coinbase',
                'symbol': 'ADA',
                'announcement_time': (datetime.now() - timedelta(days=3)).isoformat(),
                'content': 'Coinbase ADA 测试公告', 
                'source': 'manual'
            }
        ]
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for announcement in sample_announcements:
                cursor.execute('''
                    INSERT OR REPLACE INTO announcement_records 
                    (exchange, symbol, announcement_time, announcement_content, source, processed)
                    VALUES (?, ?, ?, ?, ?, FALSE)
                ''', (
                    announcement['exchange'], announcement['symbol'], announcement['announcement_time'],
                    announcement['content'], announcement['source']
                ))
                
            conn.commit()
            conn.close()
            
            logger.info("✅ 示例公告数据创建完成")
            return sample_announcements
            
        except Exception as e:
            logger.error(f"❌ 创建示例数据失败: {e}")
            return []


async def main():
    """测试Binance公告分析器"""
    logger.info("🚀 Binance公告分析器测试")
    logger.info("=" * 60)
    
    analyzer = BinanceAnnouncementAnalyzer()
    
    # 1. 创建示例数据
    sample_announcements = analyzer.create_sample_data()
    
    # 2. 批量分析公告
    if sample_announcements:
        logger.info("\n📊 开始批量分析...")
        results = await analyzer.batch_analyze_announcements(sample_announcements)
        
        # 3. 显示统计结果
        logger.info("\n📈 统计结果:")
        for exchange in ['upbit', 'binance', 'coinbase']:
            stats = analyzer.get_exchange_statistics(exchange)
            if stats.get('total_listings', 0) > 0:
                logger.info(f"\n{exchange.upper()}:")
                logger.info(f"  📊 分析数量: {stats['total_listings']}")
                logger.info(f"  📈 平均1h涨幅: {stats['avg_1hour_change']}%")
                logger.info(f"  🏆 平均最大涨幅: {stats['avg_max_gain']}%")
                logger.info(f"  ⏰ 平均达峰时间: {stats['avg_peak_time_hours']}小时")
                logger.info(f"  ✅ 成功率: {stats['success_rate']}%")
                
        logger.info("\n🎉 测试完成!")
        logger.info("💡 现在可以用这个系统分析任何交易所的上币公告!")
        
    else:
        logger.warning("⚠️ 无示例数据，跳过分析")


if __name__ == "__main__":
    asyncio.run(main())
