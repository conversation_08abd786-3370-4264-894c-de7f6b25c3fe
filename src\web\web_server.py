"""
Web管理界面服务器
提供系统监控、控制和配置管理的Web界面
"""
import asyncio
import json
import os
from datetime import datetime
from typing import Dict, Any, List

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from loguru import logger

# 添加系统路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from analysis.price_performance_analyzer import price_analyzer
    from database.exchange_database_manager import ExchangeDatabaseManager, ListingEvent
except ImportError:
    # 备用导入方式
    price_analyzer = None
    ExchangeDatabaseManager = None
    ListingEvent = None

# 临时注释掉导入，专注于上币数据功能
# from ..core.system_controller import SystemController, SystemStatus
# from ..analysis.binance_announcement_analyzer import BinanceAnnouncementAnalyzer


class WebServer:
    """Web服务器"""
    
    def __init__(self, system_controller=None, config: Dict[str, Any] = None):
        self.system_controller = system_controller
        self.config = config or {}
        self.app = FastAPI(title="交易机器人管理系统", version="1.0.0")

        # WebSocket连接管理
        self.active_connections: List[WebSocket] = []

        # Binance历史分析器（暂时注释）
        # self.binance_analyzer = BinanceAnnouncementAnalyzer()
        
        # 配置CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 设置路由
        self._setup_routes()
        
        # 注册状态变化回调（暂时注释）
        if self.system_controller:
            self.system_controller.add_status_callback(self._on_status_change)
        
        logger.info("Web服务器初始化完成")
        
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard():
            """主页面"""
            return self._get_dashboard_html()

        @self.app.get("/strategies", response_class=HTMLResponse)
        async def strategies_page():
            """策略管理页面"""
            return self._get_strategies_html()

        @self.app.get("/analysis", response_class=HTMLResponse)
        async def analysis_page():
            """历史分析页面"""
            return self._get_analysis_html()

        @self.app.get("/api/listing-data")
        async def get_listing_data():
            """获取上币数据"""
            try:
                return await self._get_listing_data()
            except Exception as e:
                logger.error(f"获取上币数据异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/refresh-listing-data")
        async def refresh_listing_data():
            """刷新上币数据 - 通过监控系统自动获取最近3个月数据"""
            try:
                return await self._refresh_listing_data()
            except Exception as e:
                logger.error(f"刷新上币数据异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
            
        @self.app.get("/api/status")
        async def get_status():
            """获取系统状态"""
            try:
                health = self.system_controller.get_system_health()
                return {
                    "status": health.status.value,
                    "uptime": health.uptime_seconds,
                    "cpu_percent": health.cpu_percent,
                    "memory_percent": health.memory_percent,
                    "active_monitors": health.active_monitors,
                    "active_positions": health.active_positions,
                    "total_signals": health.total_signals,
                    "total_trades": health.total_trades,
                    "error_count": health.error_count,
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"获取系统状态异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.post("/api/start")
        async def start_system():
            """启动系统"""
            try:
                success = await self.system_controller.start_system()
                return {"success": success, "message": "系统启动成功" if success else "系统启动失败"}
            except Exception as e:
                logger.error(f"启动系统异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.post("/api/stop")
        async def stop_system():
            """停止系统"""
            try:
                success = await self.system_controller.stop_system("Web界面请求")
                return {"success": success, "message": "系统停止成功" if success else "系统停止失败"}
            except Exception as e:
                logger.error(f"停止系统异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.post("/api/restart")
        async def restart_system():
            """重启系统"""
            try:
                success = await self.system_controller.restart_system("Web界面请求")
                return {"success": success, "message": "系统重启成功" if success else "系统重启失败"}
            except Exception as e:
                logger.error(f"重启系统异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.get("/api/exchanges")
        async def get_exchanges():
            """获取支持的交易所列表"""
            return {
                "exchanges": [
                    {"id": "upbit", "name": "Upbit", "emoji": "🏆", "description": "最激进策略"},
                    {"id": "binance", "name": "Binance", "emoji": "🥈", "description": "积极策略"},
                    {"id": "bithumb", "name": "Bithumb", "emoji": "🥉", "description": "保守策略"},
                    {"id": "coinbase", "name": "Coinbase", "emoji": "📊", "description": "最保守策略"}
                ]
            }



        @self.app.get("/api/exchanges/{exchange}/statistics")
        async def get_exchange_statistics_api(exchange: str):
            """获取指定交易所的统计数据"""
            try:
                from ..database.exchange_database_manager import ExchangeDatabaseManager
                db_manager = ExchangeDatabaseManager()

                stats = db_manager.get_exchange_statistics(exchange)
                return {
                    "exchange": exchange,
                    "statistics": stats
                }
            except Exception as e:
                logger.error(f"获取 {exchange} 统计数据异常: {e}")
                return {"error": str(e)}

        @self.app.get("/api/statistics")
        async def get_statistics():
            """获取统计信息"""
            try:
                if 'signal_processor' in self.system_controller.components:
                    processor = self.system_controller.components['signal_processor']
                    stats = processor.get_statistics()
                    return stats
                else:
                    return {"error": "信号处理器未初始化"}
            except Exception as e:
                logger.error(f"获取统计信息异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.get("/api/positions")
        async def get_positions():
            """获取持仓信息"""
            try:
                if 'signal_processor' in self.system_controller.components:
                    processor = self.system_controller.components['signal_processor']
                    if hasattr(processor, 'position_manager'):
                        positions = processor.position_manager.get_position_summary()
                        return positions
                return {"error": "持仓管理器未初始化"}
            except Exception as e:
                logger.error(f"获取持仓信息异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # 固定策略API
        @self.app.get("/api/fixed-strategy/status")
        async def get_fixed_strategy_status():
            """获取固定策略状态"""
            try:
                from ..trading.fixed_strategy import get_fixed_strategy
                fixed_strategy = get_fixed_strategy()

                if fixed_strategy:
                    active_positions = fixed_strategy.get_active_positions()
                    return {
                        "enabled": True,
                        "active_positions": len(active_positions),
                        "positions": [
                            {
                                "symbol": pos.symbol,
                                "status": pos.status.value,
                                "open_time": pos.open_time.isoformat(),
                                "remaining_percentage": pos.remaining_percentage
                            }
                            for pos in active_positions
                        ]
                    }
                else:
                    return {"enabled": False, "active_positions": 0, "positions": []}
            except Exception as e:
                logger.error(f"获取固定策略状态异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/fixed-strategy/manual-close/{symbol}")
        async def manual_close_position(symbol: str):
            """手动平仓"""
            try:
                from ..trading.fixed_strategy import get_fixed_strategy
                fixed_strategy = get_fixed_strategy()

                if fixed_strategy:
                    success = await fixed_strategy.manual_close_position(symbol)
                    return {"success": success, "message": "手动平仓成功" if success else "手动平仓失败"}
                else:
                    return {"success": False, "message": "固定策略未初始化"}
            except Exception as e:
                logger.error(f"手动平仓异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # 黑名单管理API
        @self.app.get("/api/blacklist")
        async def get_blacklist():
            """获取代币黑名单"""
            try:
                from ..core.token_blacklist_manager import get_blacklist_manager
                blacklist_manager = get_blacklist_manager()
                return blacklist_manager.get_blacklist_info()
            except Exception as e:
                logger.error(f"获取黑名单异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.delete("/api/blacklist/{token}")
        async def remove_from_blacklist(token: str):
            """从黑名单中移除代币"""
            try:
                from ..core.token_blacklist_manager import get_blacklist_manager
                blacklist_manager = get_blacklist_manager()
                success = blacklist_manager.remove_token_from_blacklist(token)
                return {"success": success, "message": f"代币 {token} 移除成功" if success else f"代币 {token} 不在黑名单中"}
            except Exception as e:
                logger.error(f"移除黑名单代币异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # Binance历史分析API
        @self.app.get("/api/exchanges")
        async def get_exchanges():
            """获取支持的交易所列表"""
            return {
                'data_source': 'binance',
                'description': '基于Binance历史行情数据的分析',
                'exchanges': [
                    {'id': 'upbit', 'name': 'Upbit', 'emoji': '🏆', 'description': '最激进策略 • 高波动率 (数据来源: Binance)'},
                    {'id': 'binance', 'name': 'Binance', 'emoji': '🥈', 'description': '积极策略 • 中等波动率 (数据来源: Binance)'},
                    {'id': 'coinbase', 'name': 'Coinbase', 'emoji': '📊', 'description': '最保守策略 • 低波动率 (数据来源: Binance)'},
                    {'id': 'bithumb', 'name': 'Bithumb', 'emoji': '🥉', 'description': '保守策略 • 稳定波动率 (数据来源: Binance)'}
                ]
            }

        @self.app.get("/api/exchanges/{exchange}/data")
        async def get_exchange_data(exchange: str, limit: int = 10):
            """获取交易所数据"""
            try:
                logger.info(f"📊 获取 {exchange.upper()} 数据...")

                # 尝试从数据库获取数据
                try:
                    from ..database.exchange_database_manager import ExchangeDatabaseManager
                    db_manager = ExchangeDatabaseManager()

                    data = db_manager.get_exchange_data(exchange, limit)
                    stats = db_manager.get_statistics(exchange)

                    if data:
                        # 转换为Web界面需要的格式
                        formatted_data = []
                        for item in data:
                            formatted_data.append({
                                'symbol': item.symbol,
                                'price': item.price,
                                'volume': item.volume,
                                'change_24h': item.change_24h or 0,
                                'timestamp': item.timestamp.isoformat(),
                                'listing_type': item.listing_type or 'unknown'
                            })

                        return {
                            "exchange": exchange,
                            "data": formatted_data,
                            "statistics": stats,
                            "total_records": len(formatted_data),
                            "source": "database"
                        }
                except Exception as db_error:
                    logger.warning(f"数据库查询失败: {db_error}")
                    # 返回空数据
                    statistics = {'total_listings': 0, 'avg_performance': 0}
                    history_data = []

                # 转换为Web界面需要的格式
                formatted_data = []
                for item in history_data:
                    formatted_item = {
                        'symbol': item['symbol'],
                        'announcement_time': item['announcement_time'],
                        'listing_price': item['announcement_price'],
                        'initial_market_cap': 0,  # Binance API不直接提供市值
                        'peak_market_cap': 0,     # 可以根据价格变化估算

                        # 价格变化数据
                        'change_1min': item['change_1min'], 'change_5min': item['change_5min'],
                        'change_15min': item['change_15min'], 'change_30min': item['change_30min'],
                        'change_1hour': item['change_1hour'], 'change_4hour': item['change_4hour'],
                        'change_12hour': item['change_12hour'], 'change_1day': item['change_1day'],
                        'change_3day': item['change_3day'],

                        # 关键指标
                        'max_gain': item['max_gain'], 'max_loss': item['max_loss'],
                        'peak_time_hours': item['peak_time_hours'],
                        'max_drawdown_from_peak': item['max_drawdown_from_peak'],
                        'volatility_3day': item['volatility_3day'],

                        # 价格时间线
                        'price_timeline': item['price_timeline']
                    }
                    formatted_data.append(formatted_item)

                return {
                    'exchange': exchange,
                    'data_source': 'binance',
                    'total_records': len(formatted_data),
                    'statistics': statistics,
                    'data': formatted_data
                }

            except Exception as e:
                logger.error(f"❌ 获取 {exchange} 数据失败: {e}")
                return {
                    'exchange': exchange,
                    'error': str(e),
                    'data_source': 'binance',
                    'total_records': 0,
                    'statistics': {'total_listings': 0},
                    'data': []
                }

        # 分析API已移除 - 固定策略不需要历史分析

            except Exception as e:
                logger.error(f"❌ 分析公告异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/contracts")
        async def get_contracts():
            """获取合约信息"""
            try:
                if 'signal_processor' in self.system_controller.components:
                    processor = self.system_controller.components['signal_processor']
                    if hasattr(processor, 'contract_manager'):
                        summary = processor.contract_manager.get_contracts_summary()
                        return summary
                return {"error": "合约管理器未初始化"}
            except Exception as e:
                logger.error(f"获取合约信息异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/contracts/refresh")
        async def refresh_contracts():
            """刷新合约信息"""
            try:
                if 'signal_processor' in self.system_controller.components:
                    processor = self.system_controller.components['signal_processor']
                    if hasattr(processor, 'contract_manager'):
                        success = await processor.contract_manager.refresh_contracts()
                        return {"success": success, "message": "合约信息刷新成功" if success else "合约信息刷新失败"}
                return {"error": "合约管理器未初始化"}
            except Exception as e:
                logger.error(f"刷新合约信息异常: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
        @self.app.get("/api/analysis-stats")
        async def get_analysis_stats():
            """获取分析统计信息"""
            try:
                if not price_analyzer:
                    return {"success": False, "message": "价格分析器未初始化"}

                import sqlite3
                conn = sqlite3.connect('exchange_data.db')
                cursor = conn.cursor()

                # 统计信息
                cursor.execute("SELECT COUNT(*) FROM listing_events")
                total_tokens = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM price_performance WHERE analyzed = 1")
                analyzed_tokens = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM price_performance WHERE analyzed = 0")
                failed_tokens = cursor.fetchone()[0]

                # 最佳表现
                cursor.execute('''
                    SELECT symbol, exchange, max_gain, time_to_peak
                    FROM price_performance
                    WHERE analyzed = 1 AND max_gain IS NOT NULL
                    ORDER BY max_gain DESC
                    LIMIT 5
                ''')
                top_performers = cursor.fetchall()

                conn.close()

                return {
                    "success": True,
                    "stats": {
                        "total_tokens": total_tokens,
                        "analyzed_tokens": analyzed_tokens,
                        "failed_tokens": failed_tokens,
                        "success_rate": round((analyzed_tokens / total_tokens * 100), 1) if total_tokens > 0 else 0,
                        "top_performers": [
                            {
                                "symbol": row[0],
                                "exchange": row[1],
                                "max_gain": round(row[2], 2),
                                "time_to_peak": row[3]
                            } for row in top_performers
                        ]
                    }
                }

            except Exception as e:
                logger.error(f"获取分析统计失败: {e}")
                return {"success": False, "message": f"获取统计失败: {str(e)}"}

        @self.app.post("/api/analyze-prices")
        async def analyze_prices():
            """分析价格表现"""
            try:
                if not price_analyzer:
                    return {"success": False, "message": "价格分析器未初始化"}

                # 异步执行价格分析
                import asyncio

                def run_analysis():
                    try:
                        # 强制重新分析所有代币
                        price_analyzer.analyze_all_listings(force_reanalyze=True)
                        return {"success": True, "message": "合约价格重新分析完成，已更新所有有USDT永续合约的代币数据"}
                    except Exception as e:
                        logger.error(f"价格分析失败: {e}")
                        return {"success": False, "message": f"分析失败: {str(e)}"}

                # 在后台运行分析
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, run_analysis)

                return result

            except Exception as e:
                logger.error(f"启动价格分析失败: {e}")
                return {"success": False, "message": f"启动分析失败: {str(e)}"}

        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket连接"""
            await self._handle_websocket(websocket)
            
    async def _handle_websocket(self, websocket: WebSocket):
        """处理WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        try:
            while True:
                try:
                    # 发送实时状态更新
                    health = self.system_controller.get_system_health()
                    await websocket.send_json({
                        "type": "status_update",
                        "data": {
                            "status": health.status.value,
                            "uptime": health.uptime_seconds,
                            "cpu_percent": health.cpu_percent,
                            "memory_percent": health.memory_percent,
                            "active_positions": health.active_positions,
                            "error_count": health.error_count,
                            "timestamp": datetime.now().isoformat()
                        }
                    })

                    await asyncio.sleep(5)  # 每5秒更新一次

                except asyncio.CancelledError:
                    # 优雅处理取消异常
                    break
                except Exception as e:
                    logger.error(f"WebSocket发送数据异常: {e}")
                    break

        except WebSocketDisconnect:
            pass
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"WebSocket异常: {e}")
        finally:
            # 清理连接
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
            try:
                if websocket.client_state != websocket.client_state.DISCONNECTED:
                    await websocket.close()
            except:
                pass
                
    async def _on_status_change(self, status):
        """系统状态变化回调"""
        try:
            message = {
                "type": "status_change",
                "data": {
                    "status": status.value,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            # 广播给所有WebSocket连接
            for connection in self.active_connections.copy():
                try:
                    await connection.send_json(message)
                except:
                    self.active_connections.remove(connection)
                    
        except Exception as e:
            logger.error(f"状态变化通知异常: {e}")
            
    def _get_dashboard_html(self) -> str:
        """获取仪表板HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能交易机器人管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .header h1 { margin-bottom: 10px; font-size: 2.2em; }
        .nav { display: flex; gap: 15px; margin-top: 15px; }
        .nav a { color: white; text-decoration: none; padding: 10px 20px; border-radius: 6px; transition: all 0.3s; background: rgba(255,255,255,0.1); }
        .nav a:hover, .nav a.active { background: rgba(255,255,255,0.2); transform: translateY(-2px); }
        .status-bar { display: flex; align-items: center; gap: 20px; margin-top: 15px; }
        .status-indicator { padding: 8px 16px; border-radius: 25px; font-weight: bold; font-size: 0.9em; transition: all 0.3s; }
        .status-running { background: #27ae60; color: white; }
        .status-stopped { background: #e74c3c; color: white; }
        .status-starting { background: #f39c12; color: white; }
        .status-unknown { background: #95a5a6; color: white; }

        /* 按钮状态样式 */
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        .btn-loading { position: relative; }
        .btn-loading::after { content: ''; position: absolute; width: 16px; height: 16px; margin: auto; border: 2px solid transparent; border-top-color: #ffffff; border-radius: 50%; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        /* 交易所选择器样式 */
        .exchange-selector { background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .exchange-selector h2 { margin-bottom: 15px; color: #2c3e50; }
        .exchange-buttons { display: flex; gap: 15px; flex-wrap: wrap; }
        .exchange-btn {
            padding: 15px 25px; border: none; border-radius: 8px; cursor: pointer;
            font-size: 1.1em; font-weight: bold; transition: all 0.3s;
            display: flex; align-items: center; gap: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .exchange-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .exchange-btn.active { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.3); }
        .exchange-btn.upbit { background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; }
        .exchange-btn.binance { background: linear-gradient(135deg, #f9ca24, #f0932b); color: white; }
        .exchange-btn.bithumb { background: linear-gradient(135deg, #6c5ce7, #a29bfe); color: white; }
        .exchange-btn.coinbase { background: linear-gradient(135deg, #00b894, #00cec9); color: white; }
        .status-stopped { background: #e74c3c; }
        .status-starting { background: #f39c12; }
        .status-error { background: #c0392b; }
        .controls { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn:hover { opacity: 0.8; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-title { font-size: 14px; color: #666; margin-bottom: 10px; }
        .metric-value { font-size: 24px; font-weight: bold; color: #2c3e50; }
        .logs { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .log-entry { padding: 5px 0; border-bottom: 1px solid #eee; font-family: monospace; font-size: 12px; }
        .progress-bar { width: 100%; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #3498db; transition: width 0.3s; }
        .strategy-item, .analysis-item, .trade-item {
            display: flex; justify-content: space-between; align-items: center;
            padding: 8px 0; border-bottom: 1px solid #eee;
        }
        .strategy-exchange, .analysis-exchange { font-weight: bold; color: #2c3e50; }
        .strategy-details, .analysis-performance { color: #666; font-size: 14px; }
        .trade-time { color: #666; font-size: 12px; }
        .trade-symbol { font-weight: bold; color: #2c3e50; }
        .trade-exchange { color: #3498db; font-size: 12px; }
        .trade-action { color: #f39c12; font-size: 12px; }
        .trade-result { font-weight: bold; }
        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 交易机器人管理系统</h1>
            <div class="nav" style="margin-top: 10px;">
                <a href="/" style="color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px; background: #3498db;">🏠 主页</a>
                <a href="/strategies" style="color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px;">🎯 策略管理</a>
                <a href="/analysis" style="color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px;">📊 历史分析</a>
            </div>
            <div class="status-bar" style="margin-top: 10px;">
                <span class="status-indicator" id="status">系统状态: 未知</span>
                <span id="uptime">运行时间: --</span>
            </div>
        </div>
        
        <!-- 交易所选择器 -->
        <div class="exchange-selector">
            <h2>📊 交易所历史数据分析</h2>
            <p style="color: #666; margin-bottom: 10px;">选择交易所查看基于Binance历史行情数据的上币公告后价格波动分析</p>
            <div style="background: #e8f4fd; padding: 12px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #3498db; font-size: 0.9em;">
                <strong>🔗 数据说明:</strong> 所有价格数据均来自Binance API，确保数据准确性和实时性
            </div>
            <div class="exchange-buttons">
                <button class="exchange-btn upbit" onclick="selectExchange('upbit')">
                    🏆 <span>Upbit<br><small>最激进策略 • Binance数据</small></span>
                </button>
                <button class="exchange-btn binance" onclick="selectExchange('binance')">
                    🥈 <span>Binance<br><small>积极策略 • Binance数据</small></span>
                </button>
                <button class="exchange-btn bithumb" onclick="selectExchange('bithumb')">
                    🥉 <span>Bithumb<br><small>保守策略 • Binance数据</small></span>
                </button>
                <button class="exchange-btn coinbase" onclick="selectExchange('coinbase')">
                    📊 <span>Coinbase<br><small>最保守策略 • Binance数据</small></span>
                </button>
            </div>
        </div>

        <!-- 交易所数据显示区域 -->
        <div id="exchange-data" style="display: none;">
            <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 id="exchange-title">交易所数据</h3>
                <div id="exchange-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <!-- 统计数据将在这里显示 -->
                </div>
                <div id="exchange-history">
                    <h4 style="margin-bottom: 15px;">📈 历史上币数据</h4>
                    <div id="history-list">
                        <!-- 历史数据列表将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>

        <div class="controls">
            <h3>系统控制</h3>
            <button class="btn btn-success" onclick="startSystem()">🚀 启动系统</button>
            <button class="btn btn-danger" onclick="stopSystem()">🛑 停止系统</button>
            <button class="btn btn-warning" onclick="restartSystem()">🔄 重启系统</button>
            <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-title">CPU使用率</div>
                <div class="metric-value" id="cpu">--%</div>
                <div class="progress-bar"><div class="progress-fill" id="cpu-bar"></div></div>
            </div>
            <div class="metric-card">
                <div class="metric-title">内存使用率</div>
                <div class="metric-value" id="memory">--%</div>
                <div class="progress-bar"><div class="progress-fill" id="memory-bar"></div></div>
            </div>
            <div class="metric-card">
                <div class="metric-title">活跃监控</div>
                <div class="metric-value" id="monitors">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">活跃持仓</div>
                <div class="metric-value" id="positions">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">总信号数</div>
                <div class="metric-value" id="signals">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">总交易数</div>
                <div class="metric-value" id="trades">--</div>
            </div>
        </div>

        <!-- 策略管理模块 -->
        <div class="logs">
            <h3>🎯 交易策略管理</h3>
            <div class="strategy-grid" id="strategy-container">
                <div class="strategy-item">
                    <span class="strategy-exchange">🏆 Upbit (最激进)</span>
                    <span class="strategy-details">仓位1.5x | 杠杆1.2x | 5级止盈</span>
                </div>
                <div class="strategy-item">
                    <span class="strategy-exchange">🥈 Binance (积极)</span>
                    <span class="strategy-details">仓位1.2x | 杠杆1.0x | 4级止盈</span>
                </div>
                <div class="strategy-item">
                    <span class="strategy-exchange">🥉 Bithumb (保守)</span>
                    <span class="strategy-details">仓位0.8x | 杠杆0.8x | 3级止盈</span>
                </div>
                <div class="strategy-item">
                    <span class="strategy-exchange">📊 Coinbase (最保守)</span>
                    <span class="strategy-details">仓位0.6x | 杠杆0.6x | 3级止盈</span>
                </div>
            </div>
        </div>

        <!-- 历史数据分析模块 -->
        <div class="logs">
            <h3>📊 历史数据分析</h3>
            <div class="analysis-grid" id="analysis-container">
                <div class="analysis-item">
                    <span class="analysis-exchange">🏆 Upbit</span>
                    <span class="analysis-performance">平均1h涨幅: <span class="positive">+25.5%</span> | 成功率: 85%</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-exchange">🥈 Binance</span>
                    <span class="analysis-performance">平均1h涨幅: <span class="positive">+18.3%</span> | 成功率: 72%</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-exchange">🥉 Bithumb</span>
                    <span class="analysis-performance">平均1h涨幅: <span class="positive">+12.7%</span> | 成功率: 65%</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-exchange">📊 Coinbase</span>
                    <span class="analysis-performance">平均1h涨幅: <span class="positive">+8.2%</span> | 成功率: 58%</span>
                </div>
            </div>
            <button class="btn btn-primary" onclick="refreshAnalysis()" style="margin-top: 10px;">🔄 刷新分析数据</button>
        </div>

        <!-- 历史交易记录模块 -->
        <div class="logs">
            <h3>📈 历史交易记录</h3>
            <div class="trade-history" id="trade-history-container">
                <div class="trade-item">
                    <span class="trade-time">2024-01-30 14:25:30</span>
                    <span class="trade-symbol">PEPE</span>
                    <span class="trade-exchange">Upbit</span>
                    <span class="trade-action">开仓</span>
                    <span class="trade-result positive">+25.8%</span>
                </div>
                <div class="trade-item">
                    <span class="trade-time">2024-01-30 13:15:22</span>
                    <span class="trade-symbol">ARB</span>
                    <span class="trade-exchange">Binance</span>
                    <span class="trade-action">开仓</span>
                    <span class="trade-result positive">+18.5%</span>
                </div>
                <div class="trade-item">
                    <span class="trade-time">2024-01-30 12:08:15</span>
                    <span class="trade-symbol">MATIC</span>
                    <span class="trade-exchange">Coinbase</span>
                    <span class="trade-action">开仓</span>
                    <span class="trade-result positive">+8.9%</span>
                </div>
            </div>
            <button class="btn btn-primary" onclick="refreshTradeHistory()" style="margin-top: 10px;">🔄 刷新交易记录</button>
        </div>
        
        <div class="logs">
            <h3>系统日志</h3>
            <div id="log-container" style="height: 300px; overflow-y: auto;">
                <div class="log-entry">系统启动中...</div>
            </div>
        </div>
    </div>
    
    <script>
        let ws = null;
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'status_update') {
                    updateStatus(data.data);
                } else if (data.type === 'status_change') {
                    addLogEntry(`系统状态变更: ${data.data.status}`);
                }
            };
            
            ws.onclose = function() {
                setTimeout(connectWebSocket, 5000);
            };
        }
        
        function updateStatus(data) {
            const statusEl = document.getElementById('status');

            // 更新状态显示
            let statusText = '';
            let statusClass = 'status-indicator';

            switch(data.status) {
                case 'running':
                    statusText = '🟢 系统运行中';
                    statusClass += ' status-running';
                    break;
                case 'stopped':
                    statusText = '🔴 系统已停止';
                    statusClass += ' status-stopped';
                    break;
                case 'starting':
                    statusText = '🟡 系统启动中';
                    statusClass += ' status-starting';
                    break;
                default:
                    statusText = '⚪ 状态未知';
                    statusClass += ' status-unknown';
            }

            statusEl.textContent = statusText;
            statusEl.className = statusClass;

            // 更新其他信息
            document.getElementById('uptime').textContent = `运行时间: ${formatUptime(data.uptime)}`;

            // 安全地更新CPU和内存信息
            const cpuEl = document.getElementById('cpu');
            const memoryEl = document.getElementById('memory');

            if (cpuEl && data.cpu_percent !== undefined) {
                cpuEl.textContent = `${data.cpu_percent.toFixed(1)}%`;
            }

            if (memoryEl && data.memory_percent !== undefined) {
                memoryEl.textContent = `${data.memory_percent.toFixed(1)}%`;
            }
            document.getElementById('monitors').textContent = data.active_monitors;
            document.getElementById('positions').textContent = data.active_positions;
            document.getElementById('signals').textContent = data.total_signals || 0;
            document.getElementById('trades').textContent = data.total_trades || 0;
            
            document.getElementById('cpu-bar').style.width = `${data.cpu_percent}%`;
            document.getElementById('memory-bar').style.width = `${data.memory_percent}%`;
        }
        
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
        
        function addLogEntry(message) {
            const container = document.getElementById('log-container');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }
        
        async function startSystem() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            try {
                // 显示加载状态
                btn.innerHTML = '⏳ 启动中...';
                btn.disabled = true;

                const response = await fetch('/api/start', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLogEntry(`✅ ${result.message}`);
                    btn.innerHTML = '✅ 启动成功';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                } else {
                    addLogEntry(`❌ ${result.message}`);
                    btn.innerHTML = '❌ 启动失败';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                }
            } catch (error) {
                addLogEntry(`❌ 启动失败: ${error.message}`);
                btn.innerHTML = '❌ 启动失败';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }

        async function stopSystem() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            try {
                // 显示加载状态
                btn.innerHTML = '⏳ 停止中...';
                btn.disabled = true;

                const response = await fetch('/api/stop', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLogEntry(`✅ ${result.message}`);
                    btn.innerHTML = '✅ 停止成功';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                } else {
                    addLogEntry(`❌ ${result.message}`);
                    btn.innerHTML = '❌ 停止失败';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                }
            } catch (error) {
                addLogEntry(`❌ 停止失败: ${error.message}`);
                btn.innerHTML = '❌ 停止失败';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }

        async function restartSystem() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            try {
                // 显示加载状态
                btn.innerHTML = '⏳ 重启中...';
                btn.disabled = true;

                const response = await fetch('/api/restart', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLogEntry(`✅ ${result.message}`);
                    btn.innerHTML = '✅ 重启成功';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 3000);
                } else {
                    addLogEntry(`❌ ${result.message}`);
                    btn.innerHTML = '❌ 重启失败';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                }
            } catch (error) {
                addLogEntry(`❌ 重启失败: ${error.message}`);
                btn.innerHTML = '❌ 重启失败';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }
        
        async function refreshData() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            try {
                // 显示加载状态
                btn.innerHTML = '⏳ 刷新中...';
                btn.disabled = true;

                const response = await fetch('/api/status');
                const data = await response.json();
                updateStatus(data);

                addLogEntry('✅ 数据刷新成功');
                btn.innerHTML = '✅ 刷新成功';

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 1000);

            } catch (error) {
                addLogEntry(`❌ 刷新失败: ${error.message}`);
                btn.innerHTML = '❌ 刷新失败';

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }

        // 交易所选择功能
        let currentExchange = null;

        async function selectExchange(exchange) {
            try {
                // 更新按钮状态
                document.querySelectorAll('.exchange-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`.exchange-btn.${exchange}`).classList.add('active');

                currentExchange = exchange;

                // 显示加载状态
                const dataDiv = document.getElementById('exchange-data');
                dataDiv.style.display = 'block';
                document.getElementById('exchange-title').innerHTML = `📊 ${exchange.toUpperCase()} 数据加载中...`;

                // 获取交易所数据
                const response = await fetch(`/api/exchanges/${exchange}/data?limit=10`);
                const result = await response.json();

                if (result.error) {
                    throw new Error(result.error);
                }

                displayExchangeData(result);

            } catch (error) {
                console.error('获取交易所数据失败:', error);
                document.getElementById('exchange-title').innerHTML = `❌ 获取 ${exchange.toUpperCase()} 数据失败: ${error.message}`;
            }
        }

        function displayExchangeData(result) {
            const exchange = result.exchange;
            const data = result.data || [];
            const stats = result.statistics || {};

            // 更新标题
            const exchangeNames = {
                'upbit': '🏆 Upbit (最激进策略) - Binance数据',
                'binance': '🥈 Binance (积极策略) - Binance数据',
                'bithumb': '🥉 Bithumb (保守策略) - Binance数据',
                'coinbase': '📊 Coinbase (最保守策略) - Binance数据'
            };

            document.getElementById('exchange-title').innerHTML = exchangeNames[exchange] || exchange.toUpperCase();

            // 显示统计数据 (使用Binance API字段)
            const statsHtml = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">📊 监控上币数</div>
                    <div style="font-size: 2em; color: #3498db; margin: 5px 0;">${stats.total_listings || 0}</div>
                    <div style="font-size: 0.8em; color: #666;">个代币</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">📈 平均1小时涨幅</div>
                    <div style="font-size: 2em; color: #27ae60; margin: 5px 0;">${stats.avg_1hour_change || 0}%</div>
                    <div style="font-size: 0.8em; color: #666;">公告后1小时</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">🏆 平均最大涨幅</div>
                    <div style="font-size: 2em; color: #f39c12; margin: 5px 0;">${stats.avg_max_gain || 0}%</div>
                    <div style="font-size: 0.8em; color: #666;">3天内峰值</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">⏰ 平均达峰时间</div>
                    <div style="font-size: 2em; color: #e74c3c; margin: 5px 0;">${stats.avg_peak_time_hours || 0}</div>
                    <div style="font-size: 0.8em; color: #666;">小时</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">📉 平均最大回撤</div>
                    <div style="font-size: 2em; color: #e74c3c; margin: 5px 0;">${stats.avg_max_drawdown || 0}%</div>
                    <div style="font-size: 0.8em; color: #666;">从峰值回撤</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #2c3e50;">✅ 盈利成功率</div>
                    <div style="font-size: 2em; color: #9b59b6; margin: 5px 0;">${stats.success_rate || 0}%</div>
                    <div style="font-size: 0.8em; color: #666;">正收益比例</div>
                </div>
            `;

            document.getElementById('exchange-stats').innerHTML = statsHtml;

            // 显示历史数据
            let historyHtml = '';

            if (data.length === 0) {
                historyHtml = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <div style="font-size: 3em; margin-bottom: 15px;">📭</div>
                        <div style="font-size: 1.2em;">暂无 ${exchange.toUpperCase()} 历史数据</div>
                        <div style="margin-top: 10px; font-size: 0.9em;">系统运行后将自动收集数据</div>
                    </div>
                `;
            } else {
                historyHtml = '<div style="display: grid; gap: 15px;">';

                data.forEach((item, index) => {
                    const changeColor = item.change_1hour >= 0 ? '#27ae60' : '#e74c3c';
                    const changeIcon = item.change_1hour >= 0 ? '📈' : '📉';

                    historyHtml += `
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid ${changeColor};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <div style="font-size: 1.4em; font-weight: bold; color: #2c3e50;">
                                    🪙 ${item.symbol}
                                </div>
                                <div style="font-size: 0.9em; color: #666;">
                                    📅 公告时间: ${item.announcement_time}
                                </div>
                            </div>

                            <!-- 基础信息 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 15px; font-size: 0.9em;">
                                <div>💰 公告时价格: $${parseFloat(item.listing_price).toFixed(8)}</div>
                                <div>⏰ 达峰时间: ${item.peak_time_hours}小时</div>
                            </div>

                            <!-- 价格变化时间线 -->
                            <div style="margin-bottom: 15px;">
                                <div style="font-weight: bold; margin-bottom: 8px; color: #2c3e50;">📈 3天价格变化时间线:</div>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 8px; font-size: 0.85em;">
                                    <div>1分钟: <span style="color: ${item.change_1min >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_1min >= 0 ? '+' : ''}${item.change_1min}%</span></div>
                                    <div>5分钟: <span style="color: ${item.change_5min >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_5min >= 0 ? '+' : ''}${item.change_5min}%</span></div>
                                    <div>1小时: <span style="color: ${item.change_1hour >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_1hour >= 0 ? '+' : ''}${item.change_1hour}%</span></div>
                                    <div>4小时: <span style="color: ${item.change_4hour >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_4hour >= 0 ? '+' : ''}${item.change_4hour}%</span></div>
                                    <div>1天: <span style="color: ${item.change_1day >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_1day >= 0 ? '+' : ''}${item.change_1day}%</span></div>
                                    <div>3天: <span style="color: ${item.change_3day >= 0 ? '#27ae60' : '#e74c3c'};">${item.change_3day >= 0 ? '+' : ''}${item.change_3day}%</span></div>
                                </div>
                            </div>

                            <!-- 关键指标 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; font-size: 0.9em;">
                                <div style="color: #27ae60;">🏆 最大涨幅: +${item.max_gain}%</div>
                                <div style="color: #e74c3c;">📉 最大跌幅: ${item.max_loss}%</div>
                                <div style="color: #e74c3c;">📊 峰值回撤: ${item.max_drawdown_from_peak}%</div>
                                <div style="color: #666;">🌊 3天波动率: ${item.volatility_3day}%</div>
                            </div>
                        </div>
                    `;
                });

                historyHtml += '</div>';
            }

            document.getElementById('history-list').innerHTML = historyHtml;
        }

        async function refreshAnalysis() {
            try {
                addLogEntry('正在刷新历史分析数据...');
                const response = await fetch('/api/historical-analysis');
                const data = await response.json();

                const container = document.getElementById('analysis-container');
                container.innerHTML = '';

                const exchanges = ['upbit', 'binance', 'bithumb', 'coinbase'];
                const exchangeNames = {
                    'upbit': '🏆 Upbit',
                    'binance': '🥈 Binance',
                    'bithumb': '🥉 Bithumb',
                    'coinbase': '📊 Coinbase'
                };

                exchanges.forEach(exchange => {
                    const exchangeData = data[exchange] || {};
                    const change1h = exchangeData.avg_change_1h || 0;
                    const successRate = (exchangeData.success_rate_1h || 0) * 100;

                    const item = document.createElement('div');
                    item.className = 'analysis-item';
                    item.innerHTML = `
                        <span class="analysis-exchange">${exchangeNames[exchange]}</span>
                        <span class="analysis-performance">平均1h涨幅: <span class="${change1h >= 0 ? 'positive' : 'negative'}">${change1h >= 0 ? '+' : ''}${change1h.toFixed(1)}%</span> | 成功率: ${successRate.toFixed(0)}%</span>
                    `;
                    container.appendChild(item);
                });

                addLogEntry('历史分析数据刷新完成');
            } catch (error) {
                addLogEntry(`刷新分析数据失败: ${error.message}`);
            }
        }

        async function refreshTradeHistory() {
            try {
                addLogEntry('正在刷新交易记录...');
                const response = await fetch('/api/statistics');
                const data = await response.json();

                // 这里可以根据实际的交易记录API来更新
                // 暂时显示模拟数据
                addLogEntry('交易记录刷新完成');
            } catch (error) {
                addLogEntry(`刷新交易记录失败: ${error.message}`);
            }
        }
        
        // 初始化
        connectWebSocket();
        refreshData();
        setInterval(refreshData, 10000); // 每10秒刷新一次
    </script>
</body>
</html>
        """
        
    async def start_server(self, host: str = "0.0.0.0", port: int = 8080):
        """启动Web服务器"""
        try:
            logger.info(f"启动Web管理界面: http://{host}:{port}")
            config = uvicorn.Config(
                app=self.app,
                host=host,
                port=port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            await server.serve()
        except Exception as e:
            logger.error(f"启动Web服务器异常: {e}")
            
    async def stop_server(self):
        """停止Web服务器"""
        try:
            # 关闭所有WebSocket连接
            for connection in self.active_connections.copy():
                await connection.close()
            self.active_connections.clear()
            logger.info("Web服务器已停止")
        except Exception as e:
            logger.error(f"停止Web服务器异常: {e}")

    def _get_strategies_html(self) -> str:
        """获取策略管理页面HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略管理 - 交易机器人</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .nav { display: flex; gap: 20px; margin-top: 10px; }
        .nav a { color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px; }
        .nav a:hover { background: rgba(255,255,255,0.2); }
        .nav a.active { background: #3498db; }
        .strategy-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .strategy-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .strategy-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c3e50; }
        .strategy-item { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .strategy-label { font-weight: 500; }
        .strategy-value { color: #666; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn:hover { opacity: 0.8; }
        .exchange-upbit { border-left: 4px solid #ff6b6b; }
        .exchange-binance { border-left: 4px solid #f39c12; }
        .exchange-bithumb { border-left: 4px solid #3498db; }
        .exchange-coinbase { border-left: 4px solid #9b59b6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 交易策略管理</h1>
            <div class="nav">
                <a href="/">🏠 主页</a>
                <a href="/strategies" class="active">🎯 策略管理</a>
                <a href="/analysis">📊 历史分析</a>
            </div>
        </div>

        <div class="strategy-grid" id="strategy-grid">
            <!-- 固定策略信息 -->
            <div class="strategy-card fixed-strategy">
                <div class="strategy-title">🎯 固定交易策略</div>
                <div class="strategy-content">
                    <div class="strategy-param">
                        <span class="param-label">固定金额:</span>
                        <span class="param-value">100 USDT</span>
                    </div>
                    <div class="strategy-param">
                        <span class="param-label">杠杆倍数:</span>
                        <span class="param-value">10x</span>
                    </div>
                    <div class="strategy-param">
                        <span class="param-label">仓位模式:</span>
                        <span class="param-value">逐仓</span>
                    </div>
                    <div class="strategy-param">
                        <span class="param-label">首次平仓:</span>
                        <span class="param-value">56秒后80%</span>
                    </div>
                    <div class="strategy-param">
                        <span class="param-label">剩余管理:</span>
                        <span class="param-value">智能平仓</span>
                    </div>
                </div>
                <div class="strategy-status" id="strategy-status">
                    <span class="status-label">状态:</span>
                    <span class="status-value" id="status-value">加载中...</span>
                </div>
                <div class="strategy-positions" id="strategy-positions">
                    <!-- 活跃仓位将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        async function loadFixedStrategy() {
            try {
                const response = await fetch('/api/fixed-strategy/status');
                const status = await response.json();

                const statusValue = document.getElementById('status-value');
                const positionsDiv = document.getElementById('strategy-positions');

                if (status.enabled) {
                    statusValue.textContent = `运行中 (${status.active_positions} 个活跃仓位)`;
                    statusValue.className = 'status-value status-active';

                    if (status.positions && status.positions.length > 0) {
                        positionsDiv.innerHTML = '<h4>活跃仓位:</h4>';
                        status.positions.forEach(pos => {
                            const posDiv = document.createElement('div');
                            posDiv.className = 'position-item';
                            posDiv.innerHTML = `
                                <span class="position-symbol">${pos.symbol}</span>
                                <span class="position-status">${pos.status}</span>
                                <span class="position-remaining">${pos.remaining_percentage}%</span>
                                <button onclick="manualClose('${pos.symbol}')" class="close-btn">手动平仓</button>
                            `;
                            positionsDiv.appendChild(posDiv);
                        });
                    } else {
                        positionsDiv.innerHTML = '<p>暂无活跃仓位</p>';
                    }
                } else {
                    statusValue.textContent = '未启用';
                    statusValue.className = 'status-value status-inactive';
                    positionsDiv.innerHTML = '';
                }
            } catch (error) {
                console.error('加载固定策略状态失败:', error);
                document.getElementById('status-value').textContent = '加载失败';
            }
        }

        async function manualClose(symbol) {
            try {
                const response = await fetch(`/api/fixed-strategy/manual-close/${symbol}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert(`${symbol} 手动平仓成功`);
                    loadFixedStrategy(); // 重新加载状态
                } else {
                    alert(`${symbol} 手动平仓失败: ${result.message}`);
                }
            } catch (error) {
                console.error('手动平仓失败:', error);
                alert('手动平仓失败');
            }
        }

        // 页面加载时获取固定策略状态
        loadFixedStrategy();

        // 每30秒刷新一次
        setInterval(loadFixedStrategy, 30000);
    </script>
</body>
</html>
        """

    def _get_analysis_html(self) -> str:
        """获取历史分析页面HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史分析 - 交易机器人</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .nav { display: flex; gap: 20px; margin-top: 10px; }
        .nav a { color: white; text-decoration: none; padding: 5px 10px; border-radius: 4px; }
        .nav a:hover { background: rgba(255,255,255,0.2); }
        .nav a.active { background: #3498db; }
        .controls { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .tabs { display: flex; gap: 10px; margin-bottom: 20px; }
        .tab { padding: 10px 20px; background: white; border: none; border-radius: 4px; cursor: pointer; }
        .tab.active { background: #3498db; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .analysis-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; }
        .analysis-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .listing-table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; }
        .listing-table th, .listing-table td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        .listing-table th { background: #f8f9fa; font-weight: 600; }
        .listing-table tr:hover { background: #f8f9fa; }
        .exchange-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600; }
        .exchange-coinbase { background: #1652f0; color: white; }
        .exchange-binance { background: #f3ba2f; color: black; }
        .exchange-upbit { background: #0066ff; color: white; }
        .exchange-bithumb { background: #ff6b35; color: white; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #3498db; }
        .stat-label { color: #666; margin-top: 5px; }
        .analysis-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c3e50; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 8px; }
        .metric-label { font-weight: 500; }
        .metric-value { color: #666; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; margin: 5px; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn:hover { opacity: 0.8; }
        .loading { text-align: center; padding: 40px; color: #666; }
        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 历史数据分析</h1>
            <div class="nav">
                <a href="/">🏠 主页</a>
                <a href="/strategies">🎯 策略管理</a>
                <a href="/analysis" class="active">📊 历史分析</a>
            </div>
        </div>

        <!-- 交易所历史数据分析 -->
        <div id="analysis-tab" class="tab-content active">
            <!-- 交易所选择器 -->
            <div class="analysis-card">
                <h3>📊 交易所历史数据分析</h3>
                <p style="color: #666; margin-bottom: 10px;">选择交易所查看最近3个月上线的所有代币及其历史数据</p>
                <div style="background: #e8f4fd; padding: 12px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #3498db; font-size: 0.9em;">
                    <strong>📊 当前显示:</strong> 公告时间、消息时间、市值 | <strong>🔄 即将添加:</strong> 1分钟涨幅、1小时涨幅（等获得API行情后）
                </div>
                <div style="margin-bottom: 15px;">
                    <button onclick="refreshListingData()" style="background: #27ae60; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                        🔄 刷新数据
                    </button>
                    <button onclick="analyzePrices(event)" style="background: #e67e22; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                        🔄 重新分析合约价格
                    </button>
                    <button onclick="showAnalysisStats()" style="background: #9b59b6; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                        📊 查看分析统计
                    </button>
                    <span id="analysis-status" style="color: #666; font-size: 14px;">重新分析所有有USDT永续合约的代币价格表现（强制更新最新数据）</span>
                </div>

                <!-- 分析统计显示区域 -->
                <div id="analysis-stats" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #9b59b6;">
                    <h4 style="margin: 0 0 10px 0; color: #9b59b6;">📊 价格分析统计</h4>
                    <div id="stats-content">加载中...</div>
                </div>
                <div class="exchange-buttons" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="exchange-btn upbit" onclick="selectExchangeHistory('upbit')" style="padding: 15px; border: none; border-radius: 8px; cursor: pointer; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        🏆 <span>Upbit<br><small>39个代币 • 最激进策略</small></span>
                    </button>
                    <button class="exchange-btn binance" onclick="selectExchangeHistory('binance')" style="padding: 15px; border: none; border-radius: 8px; cursor: pointer; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        🥈 <span>Binance<br><small>20个代币 • 积极策略</small></span>
                    </button>
                    <button class="exchange-btn bithumb" onclick="selectExchangeHistory('bithumb')" style="padding: 15px; border: none; border-radius: 8px; cursor: pointer; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                        🥉 <span>Bithumb<br><small>32个代币 • 保守策略</small></span>
                    </button>
                    <button class="exchange-btn coinbase" onclick="selectExchangeHistory('coinbase')" style="padding: 15px; border: none; border-radius: 8px; cursor: pointer; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                        📊 <span>Coinbase<br><small>51个代币 • 最保守策略</small></span>
                    </button>
                </div>
            </div>

            <!-- 选中交易所的历史数据显示区域 -->
            <div id="exchange-history-data" style="display: none;">
                <div class="analysis-card">
                    <h3 id="exchange-history-title">交易所历史数据</h3>
                    <div id="exchange-history-table">
                        <!-- 历史数据表格将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function loadAnalysis() {
            try {
                const grid = document.getElementById('analysis-grid');
                grid.innerHTML = '<div class="loading">正在加载历史分析数据...</div>';

                const response = await fetch('/api/historical-analysis');
                const analysisData = await response.json();

                grid.innerHTML = '';

                const exchangeNames = {
                    'upbit': '🏆 Upbit',
                    'binance': '🥈 Binance',
                    'bithumb': '🥉 Bithumb',
                    'coinbase': '📊 Coinbase'
                };

                for (const [exchange, data] of Object.entries(analysisData)) {
                    if (!data || Object.keys(data).length === 0) continue;

                    const card = document.createElement('div');
                    card.className = 'analysis-card';

                    const change1h = data.avg_change_1h || 0;
                    const change4h = data.avg_change_4h || 0;
                    const change1d = data.avg_change_1d || 0;

                    card.innerHTML = `
                        <div class="analysis-title">${exchangeNames[exchange] || exchange}</div>
                        <div class="metric">
                            <span class="metric-label">上币数量:</span>
                            <span class="metric-value">${data.total_listings || 0}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">平均1小时涨幅:</span>
                            <span class="metric-value ${change1h >= 0 ? 'positive' : 'negative'}">${change1h.toFixed(2)}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">平均4小时涨幅:</span>
                            <span class="metric-value ${change4h >= 0 ? 'positive' : 'negative'}">${change4h.toFixed(2)}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">平均1天涨幅:</span>
                            <span class="metric-value ${change1d >= 0 ? 'positive' : 'negative'}">${change1d.toFixed(2)}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">最后更新:</span>
                            <span class="metric-value">${data.last_updated ? new Date(data.last_updated).toLocaleString() : '未知'}</span>
                        </div>
                    `;

                    grid.appendChild(card);
                }

                if (grid.children.length === 0) {
                    grid.innerHTML = '<div class="loading">暂无历史分析数据</div>';
                }

            } catch (error) {
                console.error('加载分析数据失败:', error);
                document.getElementById('analysis-grid').innerHTML = '<div class="loading">加载失败，请重试</div>';
            }
        }

        async function refreshAnalysis() {
            try {
                const grid = document.getElementById('analysis-grid');
                grid.innerHTML = '<div class="loading">正在重新分析历史数据，请稍候...</div>';

                const response = await fetch('/api/historical-analysis/refresh', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    setTimeout(loadAnalysis, 2000); // 2秒后刷新数据
                } else {
                    grid.innerHTML = '<div class="loading">分析失败，请重试</div>';
                }

            } catch (error) {
                console.error('刷新分析失败:', error);
                document.getElementById('analysis-grid').innerHTML = '<div class="loading">刷新失败，请重试</div>';
            }
        }

        // 页面加载时初始化
        console.log('交易所历史数据分析页面已加载');

        // 刷新上币数据
        async function refreshListingData() {
            const button = event.target;
            const originalText = button.innerHTML;

            try {
                // 显示加载状态
                button.innerHTML = '🔄 正在刷新...';
                button.disabled = true;
                button.style.background = '#95a5a6';

                // 调用刷新API
                const response = await fetch('/api/refresh-listing-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // 刷新成功
                    const exchangeInfo = Object.entries(result.exchanges)
                        .map(([ex, count]) => `${ex.toUpperCase()}: ${count}`)
                        .join(', ');

                    alert(`✅ 数据刷新成功！\\n\\n📊 本次收集: ${result.total_listings}个代币\\n🏢 交易所分布: ${exchangeInfo}\\n💾 已保存: ${result.saved_to_db || result.total_listings}条到数据库\\n⏰ 数据已去重并持久化保存`);

                    // 重置上币数据缓存，强制重新加载
                    listingData = null;

                    // 如果当前有显示的交易所数据，刷新它
                    const historyData = document.getElementById('exchange-history-data');
                    if (historyData.style.display !== 'none') {
                        // 找到当前选中的交易所并重新加载
                        const title = document.getElementById('exchange-history-title').textContent;
                        const exchange = title.split(' ')[0].toLowerCase();
                        if (exchange) {
                            await selectExchangeHistory(exchange);
                        }
                    }
                } else {
                    // 刷新失败
                    alert(`❌ 数据刷新失败！\\n\\n错误信息: ${result.message}`);
                }

            } catch (error) {
                console.error('刷新数据失败:', error);
                alert(`❌ 网络错误！\\n\\n错误信息: ${error.message}`);
            } finally {
                // 恢复按钮状态
                button.innerHTML = originalText;
                button.disabled = false;
                button.style.background = '#27ae60';
            }
        }

        // 全局变量存储上币数据
        let listingData = null;

        // 选择交易所查看历史数据
        async function selectExchangeHistory(exchange) {
            console.log('选择交易所历史数据:', exchange);

            // 如果还没有加载上币数据，先加载
            if (!listingData) {
                try {
                    const response = await fetch('/api/listing-data');
                    const data = await response.json();
                    if (data.success) {
                        listingData = data;
                    } else {
                        alert('加载数据失败: ' + data.message);
                        return;
                    }
                } catch (error) {
                    alert('网络错误: ' + error.message);
                    return;
                }
            }

            // 显示历史数据区域
            document.getElementById('exchange-history-data').style.display = 'block';

            // 更新标题
            document.getElementById('exchange-history-title').textContent =
                `${exchange.toUpperCase()} 最近3个月上币历史数据`;

            // 获取该交易所的代币数据
            const exchangeTokens = listingData.exchanges[exchange] || [];

            if (exchangeTokens.length === 0) {
                document.getElementById('exchange-history-table').innerHTML =
                    '<div style="text-align: center; padding: 40px; color: #666;">该交易所暂无数据</div>';
                return;
            }

            // 生成表格
            let tableHtml = `
                <table class="listing-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>代币</th>
                            <th>公告时间</th>
                            <th>市值</th>
                            <th>1分钟</th>
                            <th>5分钟</th>
                            <th>15分钟</th>
                            <th>1小时</th>
                            <th>4小时</th>
                            <th>1天</th>
                            <th>3天</th>
                            <th>达峰时长</th>
                            <th>最大涨幅</th>
                            <th>峰值回落</th>
                            <th>波动率</th>
                            <th>1小时涨幅</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            exchangeTokens.forEach((token, index) => {
                const marketcap = token.marketcap ? `$${token.marketcap}` : '-';
                const announcementTime = token.announcement_time ?
                    token.announcement_time.substring(0, 19) : '-';

                // 格式化价格分析数据
                const formatGain = (value) => {
                    if (value === null || value === undefined) return '-';
                    const num = parseFloat(value);
                    const color = num > 0 ? '#27ae60' : num < 0 ? '#e74c3c' : '#666';
                    return `<span style="color: ${color}">${num.toFixed(2)}%</span>`;
                };

                const formatTime = (minutes) => {
                    if (!minutes) return '-';
                    if (minutes < 60) return `${minutes}分`;
                    const hours = Math.floor(minutes / 60);
                    const mins = minutes % 60;
                    return mins > 0 ? `${hours}时${mins}分` : `${hours}时`;
                };

                tableHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td><strong>${token.symbol}</strong></td>
                        <td>${announcementTime}</td>
                        <td>${marketcap}</td>
                        <td>${formatGain(token.gain_1m)}</td>
                        <td>${formatGain(token.gain_5m)}</td>
                        <td>${formatGain(token.gain_15m)}</td>
                        <td>${formatGain(token.gain_1h)}</td>
                        <td>${formatGain(token.gain_4h)}</td>
                        <td>${formatGain(token.gain_1d)}</td>
                        <td>${formatGain(token.gain_3d)}</td>
                        <td>${formatTime(token.time_to_peak)}</td>
                        <td>${formatGain(token.max_gain)}</td>
                        <td>${formatGain(token.peak_pullback)}</td>
                        <td>${token.volatility ? token.volatility.toFixed(2) + '%' : '-'}</td>
                    </tr>
                `;
            });

            tableHtml += `
                    </tbody>
                </table>
                <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 14px; color: #666;">
                    📊 显示 ${exchangeTokens.length} 个代币 |
                    ⏳ 1分钟涨幅、1小时涨幅等数据将在获得API行情后自动填入 |
                    🔄 届时将进行完整的历史分析
                </div>
            `;

            document.getElementById('exchange-history-table').innerHTML = tableHtml;
        }

        // 分析价格表现
        async function analyzePrices(event) {
            // 阻止默认行为
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const button = event ? event.target : document.querySelector('button[onclick="analyzePrices()"]');
            const originalText = button.innerHTML;
            const statusSpan = document.getElementById('analysis-status');

            try {
                // 更新按钮状态
                button.innerHTML = '🔄 重新分析中...';
                button.disabled = true;
                button.style.background = '#95a5a6';
                statusSpan.textContent = '正在重新分析所有代币的合约价格表现，这可能需要几分钟时间...';
                statusSpan.style.color = '#e67e22';

                // 发送分析请求
                console.log('开始发送价格分析请求...');
                const response = await fetch('/api/analyze-prices', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('收到响应:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('分析结果:', result);

                if (result.success) {
                    statusSpan.textContent = `✅ ${result.message}`;
                    statusSpan.style.color = '#27ae60';

                    // 添加刷新按钮
                    const refreshBtn = document.createElement('button');
                    refreshBtn.textContent = '🔄 刷新查看结果';
                    refreshBtn.style.cssText = 'margin-left: 10px; padding: 5px 10px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;';
                    refreshBtn.onclick = () => {
                        // 强制刷新，清除缓存
                        window.location.reload(true);
                    };

                    statusSpan.appendChild(document.createElement('br'));
                    statusSpan.appendChild(refreshBtn);
                } else {
                    statusSpan.textContent = `❌ ${result.message}`;
                    statusSpan.style.color = '#e74c3c';
                }

            } catch (error) {
                console.error('分析价格失败:', error);
                statusSpan.textContent = `❌ 网络错误: ${error.message}`;
                statusSpan.style.color = '#e74c3c';
            } finally {
                // 恢复按钮状态
                button.innerHTML = originalText;
                button.disabled = false;
                button.style.background = '#e67e22';
            }
        }

        // 显示分析统计
        async function showAnalysisStats() {
            const statsDiv = document.getElementById('analysis-stats');
            const contentDiv = document.getElementById('stats-content');

            try {
                // 显示统计区域
                statsDiv.style.display = 'block';
                contentDiv.innerHTML = '⏳ 加载统计数据...';

                // 获取统计数据
                const response = await fetch('/api/analysis-stats');
                const result = await response.json();

                if (result.success) {
                    const stats = result.stats;

                    let html = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                            <div style="text-align: center; padding: 10px; background: white; border-radius: 4px;">
                                <div style="font-size: 24px; font-weight: bold; color: #3498db;">${stats.total_tokens}</div>
                                <div style="color: #666;">总代币数</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: white; border-radius: 4px;">
                                <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${stats.analyzed_tokens}</div>
                                <div style="color: #666;">已分析</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: white; border-radius: 4px;">
                                <div style="font-size: 24px; font-weight: bold; color: #e74c3c;">${stats.failed_tokens}</div>
                                <div style="color: #666;">分析失败</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: white; border-radius: 4px;">
                                <div style="font-size: 24px; font-weight: bold; color: #f39c12;">${stats.success_rate}%</div>
                                <div style="color: #666;">成功率</div>
                            </div>
                        </div>
                    `;

                    if (stats.top_performers && stats.top_performers.length > 0) {
                        html += `
                            <h5 style="margin: 15px 0 10px 0; color: #9b59b6;">🏆 表现最佳的代币</h5>
                            <div style="background: white; border-radius: 4px; overflow: hidden;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">代币</th>
                                            <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">交易所</th>
                                            <th style="padding: 8px; text-align: right; border-bottom: 1px solid #dee2e6;">最大涨幅</th>
                                            <th style="padding: 8px; text-align: right; border-bottom: 1px solid #dee2e6;">达峰时长</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;

                        stats.top_performers.forEach((token, index) => {
                            const gainColor = token.max_gain > 0 ? '#27ae60' : '#e74c3c';
                            const timeText = token.time_to_peak ? `${Math.floor(token.time_to_peak / 60)}时${token.time_to_peak % 60}分` : '-';

                            html += `
                                <tr style="border-bottom: 1px solid #f8f9fa;">
                                    <td style="padding: 8px; font-weight: bold;">${token.symbol}</td>
                                    <td style="padding: 8px;">${token.exchange.toUpperCase()}</td>
                                    <td style="padding: 8px; text-align: right; color: ${gainColor}; font-weight: bold;">+${token.max_gain}%</td>
                                    <td style="padding: 8px; text-align: right;">${timeText}</td>
                                </tr>
                            `;
                        });

                        html += `
                                    </tbody>
                                </table>
                            </div>
                        `;
                    }

                    html += `
                        <div style="margin-top: 15px; text-align: center;">
                            <button onclick="document.getElementById('analysis-stats').style.display='none'"
                                    style="background: #95a5a6; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                                关闭统计
                            </button>
                        </div>
                    `;

                    contentDiv.innerHTML = html;
                } else {
                    contentDiv.innerHTML = `❌ 获取统计失败: ${result.message}`;
                }

            } catch (error) {
                console.error('获取分析统计失败:', error);
                contentDiv.innerHTML = `❌ 网络错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
        """

    async def _get_listing_data(self) -> Dict[str, Any]:
        """从数据库获取上币数据"""
        try:
            # 导入数据库管理器
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from database import exchange_db

            # 获取价格分析数据
            performance_data = {}
            if price_analyzer:
                try:
                    performances = price_analyzer.get_performance_data()
                    for perf in performances:
                        key = f"{perf.symbol}_{perf.exchange}"
                        performance_data[key] = perf
                except Exception as e:
                    logger.error(f"获取价格分析数据失败: {e}")

            # 获取所有交易所的上币事件
            all_listings = []
            exchanges_data = {}

            for exchange in ['binance', 'coinbase', 'upbit', 'bithumb']:
                try:
                    # 获取该交易所的上币事件
                    events = exchange_db.get_listing_events(exchange)

                    exchange_listings = []
                    for event in events:
                        # 基础数据
                        listing_record = {
                            'symbol': event.symbol,
                            'exchange': event.exchange,
                            'announcement_time': event.announcement_time.isoformat() if event.announcement_time else '',
                            'marketcap': event.marketcap or '',
                            'source': event.source,
                            'processed': event.processed
                        }

                        # 合并价格分析数据
                        perf_key = f"{event.symbol}_{event.exchange}"
                        if perf_key in performance_data:
                            perf = performance_data[perf_key]
                            listing_record.update({
                                'gain_1m': perf.gain_1m,
                                'gain_5m': perf.gain_5m,
                                'gain_15m': perf.gain_15m,
                                'gain_1h': perf.gain_1h,
                                'gain_4h': perf.gain_4h,
                                'gain_1d': perf.gain_1d,
                                'gain_3d': perf.gain_3d,
                                'time_to_peak': perf.time_to_peak,
                                'max_gain': perf.max_gain,
                                'peak_pullback': perf.peak_pullback,
                                'volatility': perf.volatility,
                                'analyzed': perf.analyzed
                            })
                        else:
                            # 默认值
                            listing_record.update({
                                'gain_1m': None, 'gain_5m': None, 'gain_15m': None,
                                'gain_1h': None, 'gain_4h': None, 'gain_1d': None,
                                'gain_3d': None, 'time_to_peak': None, 'max_gain': None,
                                'peak_pullback': None, 'volatility': None, 'analyzed': False
                            })

                        exchange_listings.append(listing_record)
                        all_listings.append(listing_record)

                    # 按时间排序
                    exchange_listings.sort(key=lambda x: x['announcement_time'], reverse=True)
                    exchanges_data[exchange] = exchange_listings

                    logger.info(f"从数据库获取 {exchange} 上币数据: {len(exchange_listings)} 条")

                except Exception as e:
                    logger.warning(f"获取 {exchange} 数据失败: {e}")
                    exchanges_data[exchange] = []

            # 统计信息
            stats = {
                'total_listings': len(all_listings),
                'exchanges': {
                    exchange: len(tokens)
                    for exchange, tokens in exchanges_data.items()
                },
                'latest_update': datetime.now().isoformat(),
                'data_source': 'database'
            }

            logger.info(f"从数据库获取上币数据完成: 总计 {len(all_listings)} 条")

            return {
                "success": True,
                "stats": stats,
                "exchanges": exchanges_data,
                "data": all_listings
            }

        except Exception as e:
            logger.error(f"从数据库读取上币数据失败: {e}")
            # 如果数据库读取失败，回退到文件读取
            return await self._get_listing_data_from_file()

    async def _get_listing_data_from_file(self) -> Dict[str, Any]:
        """从文件获取上币数据（备用方案）"""
        try:
            # 查找最新的上币数据文件
            listing_file = self._find_latest_listing_file()

            if not listing_file:
                return {
                    "success": False,
                    "message": "未找到上币数据文件",
                    "data": []
                }

            # 读取数据
            with open(listing_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # 检查是否是数据库格式
                if isinstance(data, dict) and 'listings' in data:
                    listings = data['listings']  # 数据库格式
                else:
                    listings = data  # 简单格式

            # 按交易所分组
            exchanges = {}
            for listing in listings:
                exchange = listing['exchange']
                if exchange not in exchanges:
                    exchanges[exchange] = []
                exchanges[exchange].append(listing)

            # 按时间排序
            for exchange in exchanges:
                exchanges[exchange].sort(key=lambda x: x['announcement_time'], reverse=True)

            # 统计信息
            stats = {
                'total_listings': len(listings),
                'exchanges': {
                    exchange: len(tokens)
                    for exchange, tokens in exchanges.items()
                },
                'latest_update': datetime.now().isoformat(),
                'data_source': 'file'
            }

            return {
                "success": True,
                "stats": stats,
                "exchanges": exchanges,
                "data": listings
            }

        except Exception as e:
            logger.error(f"从文件读取上币数据失败: {e}")
            return {
                "success": False,
                "message": f"读取数据失败: {str(e)}",
                "data": []
            }

    def _find_latest_listing_file(self) -> str:
        """查找最新的上币数据文件"""
        try:
            # 在当前目录和上级目录查找
            search_paths = ['.', '..']

            for path in search_paths:
                if os.path.exists(path):
                    files = os.listdir(path)
                    listing_files = [
                        f for f in files
                        if (f.startswith('simple_listings_') or f.startswith('listings_database_')) and f.endswith('.json')
                    ]

                    if listing_files:
                        # 按文件名排序，取最新的
                        listing_files.sort(reverse=True)
                        latest_file = os.path.join(path, listing_files[0])
                        logger.info(f"找到上币数据文件: {latest_file}")
                        return latest_file

            logger.warning("未找到上币数据文件")
            return ""

        except Exception as e:
            logger.error(f"查找上币数据文件失败: {e}")
            return ""

    async def _refresh_listing_data(self) -> Dict[str, Any]:
        """刷新上币数据 - 通过监控系统自动获取"""
        try:
            logger.info("开始刷新上币数据...")

            # 创建上币数据收集器
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from monitors.listing_data_collector import ListingDataCollector

            collector = ListingDataCollector()

            # 初始化收集器
            if not await collector.initialize():
                return {
                    "success": False,
                    "message": "监控系统初始化失败",
                    "total_listings": 0,
                    "exchanges": {}
                }

            try:
                # 收集最近3个月的上币数据
                logger.info("正在收集最近3个月的上币数据...")
                listings_data = await collector.collect_listings(days=90)

                if not listings_data or len(listings_data) == 0:
                    return {
                        "success": False,
                        "message": "未收集到任何上币数据",
                        "total_listings": 0,
                        "exchanges": {}
                    }

                # 保存到数据库
                saved_count = await self._save_listings_to_database(listings_data)

                # 按交易所分组统计
                exchanges = {}
                for listing in listings_data:
                    exchange = listing.get('exchange', 'unknown')
                    if exchange not in exchanges:
                        exchanges[exchange] = 0
                    exchanges[exchange] += 1

                logger.info(f"成功刷新上币数据: {len(listings_data)}条记录，保存到数据库: {saved_count}条")

                return {
                    "success": True,
                    "message": "数据刷新成功",
                    "total_listings": len(listings_data),
                    "exchanges": exchanges,
                    "saved_to_db": saved_count
                }

            finally:
                # 清理资源
                await collector.cleanup()

        except ImportError:
            logger.error("ListingDataCollector模块未找到，使用备用方案")
            return await self._refresh_listing_data_fallback()
        except Exception as e:
            logger.error(f"刷新上币数据失败: {e}")
            return {
                "success": False,
                "message": f"刷新失败: {str(e)}",
                "total_listings": 0,
                "exchanges": {}
            }

    async def _refresh_listing_data_fallback(self) -> Dict[str, Any]:
        """备用方案：直接使用内置收集器"""
        try:
            logger.info("使用备用方案：直接调用内置收集器...")

            # 直接使用内置的收集逻辑
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from monitors.listing_data_collector import ListingDataCollector

            collector = ListingDataCollector()

            # 初始化收集器
            if not await collector.initialize():
                return {
                    "success": False,
                    "message": "内置收集器初始化失败",
                    "total_listings": 0,
                    "exchanges": {}
                }

            try:
                # 收集数据
                listings_data = await collector.collect_listings(days=90)

                if not listings_data or len(listings_data) == 0:
                    return {
                        "success": False,
                        "message": "未收集到任何上币数据",
                        "total_listings": 0,
                        "exchanges": {}
                    }

                # 保存数据
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"simple_listings_{timestamp}.json"

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(listings_data, f, indent=2, ensure_ascii=False)

                logger.info(f"数据已保存到: {filename}")

                # 统计信息
                exchanges = {}
                for listing in listings_data:
                    exchange = listing.get('exchange', 'unknown')
                    if exchange not in exchanges:
                        exchanges[exchange] = 0
                    exchanges[exchange] += 1

                return {
                    "success": True,
                    "message": "通过内置收集器刷新成功",
                    "total_listings": len(listings_data),
                    "exchanges": exchanges
                }

            finally:
                await collector.cleanup()

        except ImportError as e:
            logger.error(f"导入收集器失败: {e}")
            return {
                "success": False,
                "message": f"导入收集器失败: {str(e)}",
                "total_listings": 0,
                "exchanges": {}
            }
        except Exception as e:
            logger.error(f"备用方案执行失败: {e}")
            return {
                "success": False,
                "message": f"备用方案失败: {str(e)}",
                "total_listings": 0,
                "exchanges": {}
            }

    async def _save_listings_to_database(self, listings_data: list) -> int:
        """保存上币数据到数据库"""
        try:
            # 导入数据库管理器
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from database import exchange_db, ListingEvent

            saved_count = 0

            for listing in listings_data:
                try:
                    # 创建上币事件对象
                    event = ListingEvent(
                        exchange=listing['exchange'],
                        symbol=listing['symbol'],
                        announcement_time=datetime.fromisoformat(listing['announcement_time'].replace(' ', 'T')),
                        announcement_content=listing.get('message_text', ''),
                        marketcap=listing.get('marketcap', ''),
                        source='telegram_monitor'
                    )

                    # 保存到数据库
                    exchange_db.save_listing_event(event)
                    saved_count += 1

                except Exception as e:
                    logger.warning(f"保存上币事件失败 {listing['exchange']}-{listing['symbol']}: {e}")
                    continue

            logger.info(f"成功保存 {saved_count}/{len(listings_data)} 条上币数据到数据库")

            # 同时保存一份到文件作为备份
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"listings_backup_{timestamp}.json"

            backup_data = {
                'metadata': {
                    'created_at': datetime.now().isoformat(),
                    'total_records': len(listings_data),
                    'collection_method': 'monitoring_system',
                    'data_period_days': 90,
                    'saved_to_db': saved_count
                },
                'listings': listings_data
            }

            with open(backup_filename, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            logger.info(f"备份文件已保存: {backup_filename}")

            return saved_count

        except Exception as e:
            logger.error(f"保存到数据库失败: {e}")
            # 如果数据库保存失败，至少保存到文件
            try:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                fallback_filename = f"listings_fallback_{timestamp}.json"

                with open(fallback_filename, 'w', encoding='utf-8') as f:
                    json.dump(listings_data, f, indent=2, ensure_ascii=False)

                logger.info(f"数据库保存失败，已保存到备用文件: {fallback_filename}")
                return len(listings_data)

            except Exception as e2:
                logger.error(f"备用文件保存也失败: {e2}")
                return 0

if __name__ == "__main__":
    """直接运行web服务器"""
    import uvicorn

    print("🚀 启动交易机器人Web管理界面...")
    print("📊 访问 http://localhost:8000 查看界面")

    # 创建web服务器实例
    web_server = WebServer()

    # 启动服务器
    uvicorn.run(web_server.app, host="0.0.0.0", port=8000)
