@echo off
REM 智能交易机器人 Docker 构建脚本 (Windows)

echo 🐳 智能交易机器人 Docker 构建脚本
echo =====================================

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

echo ✅ Docker环境检查通过

REM 检查必要文件
if not exist "config.yaml" (
    echo ❌ 缺少配置文件: config.yaml
    pause
    exit /b 1
)

if not exist ".env" (
    echo ❌ 缺少环境变量文件: .env
    echo 💡 请复制 .env.template 为 .env 并填入正确的API密钥
    pause
    exit /b 1
)

echo ✅ 文件检查完成

REM 构建镜像
echo 🔨 开始构建Docker镜像...
docker-compose build --no-cache
if %errorlevel% neq 0 (
    echo ❌ 镜像构建失败
    pause
    exit /b 1
)

echo ✅ 镜像构建完成

REM 启动服务
echo 🚀 启动服务...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ❌ 服务启动失败
    pause
    exit /b 1
)

echo ✅ 服务启动完成

REM 显示状态
echo.
echo 📊 服务状态:
docker-compose ps

echo.
echo 🎉 智能交易机器人已在Docker中启动！
echo 🌐 Web界面: http://localhost:8080
echo 📝 查看日志: docker-compose logs -f goldbot
echo 🛑 停止服务: docker-compose down

pause
