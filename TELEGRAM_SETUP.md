# Telegram监控配置指南

## 问题说明

如果看到以下错误信息：
```
EOF when reading a line
Telegram监控异常: EOF when reading a line
系统错误率过高: 100.0%
```

这表示Telegram监控器需要交互式认证，但在Docker容器中无法完成。

## 解决方案

### 方案1：在主机上生成会话文件（推荐）

1. **在主机上运行认证脚本**：
```bash
# 创建认证脚本
cat > telegram_auth.py << 'EOF'
import asyncio
from telethon import TelegramClient

async def main():
    api_id = 26145597  # 替换为你的API ID
    api_hash = "859206f58db62ec957089a7e9ff11d38"  # 替换为你的API Hash
    phone = "+8613375386798"  # 替换为你的手机号
    
    client = TelegramClient('telegram_session', api_id, api_hash)
    await client.start(phone=phone)
    print("认证成功！会话文件已生成")
    await client.disconnect()

if __name__ == '__main__':
    asyncio.run(main())
EOF

# 安装依赖
pip install telethon

# 运行认证
python telegram_auth.py
```

2. **将会话文件复制到项目目录**：
```bash
# 会话文件应该生成在当前目录
ls telegram_session.session

# 确保文件在goldcoin项目根目录中
cp telegram_session.session /path/to/goldcoin/
```

3. **重启Docker容器**：
```bash
cd /path/to/goldcoin
docker-compose restart
```

### 方案2：暂时禁用Telegram监控

如果暂时不需要Telegram监控功能，可以修改配置文件：

```yaml
# 在config.yaml中添加
monitoring:
  telegram:
    enabled: false  # 添加这行禁用Telegram监控
    api_id: 26145597
    # ... 其他配置保持不变
```

### 方案3：使用现有会话文件

如果你已经有Telegram会话文件：

1. **检查会话文件**：
```bash
ls goldbot_session.session telegram_session.session
```

2. **确保文件位置正确**：
会话文件应该在项目根目录（与docker-compose.yml同级）

3. **检查文件权限**：
```bash
chmod 644 *.session
```

## 验证配置

配置完成后，重启系统，应该看到：

```
✅ Telegram客户端连接成功
✅ Telegram监控器已关联飞书通知
```

而不是：
```
❌ Telegram客户端启动失败
EOF when reading a line
```

## 注意事项

1. **API配置**：确保config.yaml中的api_id、api_hash、phone配置正确
2. **网络连接**：确保Docker容器能够访问Telegram服务器
3. **会话文件**：会话文件包含认证信息，请妥善保管
4. **多设备登录**：同一个账号在多个设备同时登录可能会导致会话失效

## 故障排除

如果问题仍然存在：

1. 检查Docker容器日志：
```bash
docker-compose logs goldbot-trading
```

2. 验证API配置是否正确
3. 确认网络连接正常
4. 重新生成会话文件

配置成功后，系统错误率应该降到正常水平。