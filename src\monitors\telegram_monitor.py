"""
Telegram频道监控模块
监控@bswnews频道的新消息
"""
import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Callable
from loguru import logger

try:
    from telethon import TelegramClient, events
    from telethon.tl.types import Channel
except ImportError:
    logger.error("请安装telethon: pip install telethon")
    raise

from ..models import TradingSignal, SignalSource, TelegramMessage, TokenInfo
from ..utils import extract_market_cap
from .unified_rules import unified_rules
from ..core.token_blacklist_manager import get_blacklist_manager


class TelegramMonitor:
    """Telegram频道监控器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_id = config.get('api_id')
        self.api_hash = config.get('api_hash')
        self.phone = config.get('phone')

        # 支持多个频道
        channels = config.get('channels', [])
        if isinstance(channels, list):
            self.channels = channels
        else:
            # 兼容旧配置格式
            self.channels = [config.get('channel', '@BWEnews')]

        # 优先使用配置文件的关键词，否则使用统一规则
        self.keywords = config.get('keywords', unified_rules.get_all_keywords())
        self.exclude_keywords = config.get('exclude_keywords', unified_rules.exclude_keywords)

        # 初始化黑名单管理器
        self.blacklist_manager = get_blacklist_manager()

        logger.info(f"📡 Telegram监控关键词: {len(self.keywords)}个")
        logger.info(f"📡 排除关键词: {len(self.exclude_keywords)}个")
        
        if not all([self.api_id, self.api_hash, self.phone]):
            raise ValueError("Telegram API配置不完整，请检查api_id, api_hash, phone")
            
        self.client: Optional[TelegramClient] = None
        self.running = False
        self.last_message_id = 0
        self.signal_callback: Optional[Callable] = None
        self.connection_retries = 0
        self.max_retries = 5
        self.feishu_notifier = None  # 将在初始化时设置
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.client = TelegramClient('telegram_session', self.api_id, self.api_hash)
        await self.client.start(phone=self.phone)
        logger.info("Telegram客户端连接成功")
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.client:
            await self.client.disconnect()
            logger.info("Telegram客户端已断开连接")
            
    async def get_channel_entity(self):
        """获取频道实体"""
        try:
            entity = await self.client.get_entity(self.channel)
            if isinstance(entity, Channel):
                logger.info(f"成功获取频道: {entity.title} ({self.channel})")
                return entity
            else:
                logger.error(f"指定的实体不是频道: {self.channel}")
                return None
        except Exception as e:
            logger.error(f"获取频道实体失败: {e}")
            return None
            
    def is_relevant_message(self, message_text: str) -> bool:
        """检查消息是否相关 - 包括上币消息和Token Swap消息"""
        # 检查是否为上币消息
        if unified_rules.is_listing_message(message_text):
            return True

        # 检查是否为Token Swap消息
        if self.blacklist_manager.is_token_swap_message(message_text):
            return True

        return False
        
    def extract_signal_from_message(self, message: TelegramMessage) -> Optional[TradingSignal]:
        """从Telegram消息中提取交易信号"""
        if not self.is_relevant_message(message.text):
            logger.debug(f"消息不包含关键词，跳过: {message.text[:50]}...")
            return None
            
        # 提取代币信息 - 使用统一规则
        tokens_symbols = unified_rules.extract_symbols_from_text(message.text)
        if not tokens_symbols:
            logger.debug(f"未从消息中提取到代币信息: {message.text[:50]}...")
            return None
            
        # 提取市值信息
        market_cap = extract_market_cap(message.text)
        
        # 创建代币信息
        tokens = []
        for symbol in tokens_symbols:
            token_info = TokenInfo(
                symbol=symbol,
                market_cap=market_cap if len(tokens_symbols) == 1 else None
            )
            tokens.append(token_info)
            
        # 创建交易信号 - 固定策略统一处理
        signal = TradingSignal(
            source=SignalSource.TELEGRAM,
            symbol=tokens[0].symbol,  # 使用第一个代币作为主要信号
            timestamp=message.date,
            content=message.text,
            tokens=tokens,
            confidence=0.9,  # 固定策略统一置信度
            priority=1,  # 固定策略统一优先级
            exchange=detected_exchange,  # 使用检测到的交易所
            metadata={
                'message_id': message.id,
                'channel': message.channel,
                'sender': message.sender,
                'market_cap': market_cap,
                'detected_exchange': detected_exchange
            }
        )
        
        logger.info(f"从Telegram消息提取到交易信号: {signal.symbol} - {message.text[:100]}...")
        return signal
        
    async def get_recent_messages(self, limit: int = 10) -> List[TelegramMessage]:
        """获取最近的消息"""
        if not self.client:
            raise RuntimeError("Telegram客户端未初始化")
            
        try:
            entity = await self.get_channel_entity()
            if not entity:
                return []
                
            messages = []
            async for message in self.client.iter_messages(entity, limit=limit):
                if message.text:
                    telegram_msg = TelegramMessage(
                        id=message.id,
                        text=message.text,
                        date=message.date,
                        channel=self.channel,
                        sender=message.sender_id
                    )
                    messages.append(telegram_msg)
                    
            return messages
            
        except Exception as e:
            logger.error(f"获取Telegram消息失败: {e}")
            return []
            
    async def check_new_messages(self) -> List[TradingSignal]:
        """检查新消息"""
        try:
            messages = await self.get_recent_messages(limit=20)
            if not messages:
                return []
                
            # 过滤新消息
            new_messages = [msg for msg in messages if msg.id > self.last_message_id]
            
            if new_messages:
                # 更新最后消息ID
                self.last_message_id = max(msg.id for msg in new_messages)
                logger.info(f"检测到 {len(new_messages)} 条新的Telegram消息")
                
                # 提取交易信号
                signals = []
                for message in new_messages:
                    signal = self.extract_signal_from_message(message)
                    if signal:
                        signals.append(signal)
                        
                return signals
                
        except Exception as e:
            logger.error(f"检查Telegram新消息时发生异常: {e}")
            
        return []
        
    async def start_monitoring(self, signal_callback: Optional[Callable] = None):
        """开始监控 - 使用事件驱动的长连接模式"""
        logger.info(f"🚀 开始监控Telegram频道: {self.channels} (长连接模式)")
        self.running = True
        self.signal_callback = signal_callback

        try:
            # 启动长连接监控
            await self._start_event_monitoring()
        except Exception as e:
            logger.error(f"启动事件监控失败: {e}")
            # 发送错误通知
            await self._send_error_notification(
                "长连接启动失败",
                str(e),
                "事件驱动监控启动失败，将切换到轮询模式"
            )
            # 如果事件监控失败，回退到轮询模式
            logger.info("回退到轮询监控模式")
            await self._start_polling_monitoring()

    async def _start_event_monitoring(self):
        """事件驱动的长连接监控"""
        if not self.client:
            raise RuntimeError("Telegram客户端未初始化")

        # 获取要监控的频道实体
        channel_entities = []
        for channel in self.channels:
            try:
                entity = await self.client.get_entity(channel)
                channel_entities.append(entity)
                logger.info(f"✅ 成功连接频道: {channel}")
            except Exception as e:
                logger.error(f"❌ 连接频道失败 {channel}: {e}")

        if not channel_entities:
            raise RuntimeError("没有可用的频道实体")

        # 注册新消息事件处理器
        @self.client.on(events.NewMessage(chats=channel_entities))
        async def handle_new_message(event):
            try:
                message_text = event.message.message
                if not message_text:
                    return

                logger.debug(f"📨 收到新消息: {message_text[:100]}...")

                # 检查消息是否相关
                if not self.is_relevant_message(message_text):
                    return

                # 创建消息对象
                telegram_message = TelegramMessage(
                    id=event.message.id,
                    text=message_text,
                    date=event.message.date,
                    chat_id=event.chat_id,
                    sender_id=event.sender_id
                )

                # 提取交易信号
                signal = self.extract_signal_from_message(telegram_message)
                if signal and self.signal_callback:
                    logger.info(f"🎯 检测到交易信号: {signal.symbol}")
                    await self.signal_callback(signal)

            except Exception as e:
                logger.error(f"处理新消息异常: {e}")

        logger.info("🔗 长连接事件监听器已注册")

        # 保持连接活跃
        while self.running:
            try:
                # 检查连接状态
                if not self.client.is_connected():
                    logger.warning("⚠️ Telegram连接断开，尝试重连...")
                    # 发送连接断开通知
                    await self._send_error_notification(
                        "连接断开",
                        "Telegram长连接意外断开",
                        "正在尝试自动重连..."
                    )

                    await self.client.connect()
                    self.connection_retries = 0
                    logger.info("✅ Telegram重连成功")

                    # 发送重连成功通知
                    await self._send_recovery_notification(
                        "长连接恢复",
                        "Telegram长连接已成功重新建立"
                    )

                # 每30秒检查一次连接状态
                await asyncio.sleep(30)

            except Exception as e:
                logger.error(f"维持连接时发生异常: {e}")
                self.connection_retries += 1

                if self.connection_retries >= self.max_retries:
                    logger.error(f"❌ 连接重试次数超限 ({self.max_retries})，切换到轮询模式")
                    # 发送故障转移通知
                    await self._send_error_notification(
                        "故障转移",
                        f"长连接重试次数超限 ({self.max_retries}次)",
                        "系统将自动切换到轮询监控模式以确保服务连续性"
                    )
                    await self._start_polling_monitoring()
                    # 发送轮询模式启动通知
                    await self._send_recovery_notification(
                        "轮询监控模式",
                        "已切换到轮询监控模式，系统继续正常运行"
                    )
                    break

                await asyncio.sleep(10 * self.connection_retries)  # 指数退避

    async def _start_polling_monitoring(self):
        """轮询监控模式 (备用方案)"""
        logger.info("📡 启动轮询监控模式")

        # 初始化最后消息ID
        try:
            recent_messages = await self.get_recent_messages(limit=1)
            if recent_messages:
                self.last_message_id = recent_messages[0].id
                logger.info(f"初始化最后消息ID: {self.last_message_id}")
        except Exception as e:
            logger.error(f"初始化最后消息ID失败: {e}")

        # 开始轮询循环
        while self.running:
            try:
                signals = await self.check_new_messages()
                if signals and self.signal_callback:
                    for signal in signals:
                        await self.signal_callback(signal)

                await asyncio.sleep(3)  # 轮询间隔3秒

            except Exception as e:
                logger.error(f"轮询监控异常: {e}")
                # 发送轮询异常通知
                await self._send_error_notification(
                    "轮询监控异常",
                    str(e),
                    "轮询监控过程中发生异常，系统将继续尝试"
                )
                await asyncio.sleep(10)  # 出错时等待更长时间
                
    def stop_monitoring(self):
        """停止监控"""
        logger.info("🛑 停止监控Telegram频道")
        self.running = False

    async def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态"""
        if not self.client:
            return {
                'connected': False,
                'status': 'client_not_initialized',
                'retries': self.connection_retries,
                'channels': self.channels
            }

        try:
            is_connected = self.client.is_connected()
            return {
                'connected': is_connected,
                'status': 'connected' if is_connected else 'disconnected',
                'retries': self.connection_retries,
                'channels': self.channels,
                'running': self.running
            }
        except Exception as e:
            return {
                'connected': False,
                'status': f'error: {e}',
                'retries': self.connection_retries,
                'channels': self.channels
            }

    async def ensure_connection(self) -> bool:
        """确保连接正常"""
        try:
            if not self.client:
                logger.error("Telegram客户端未初始化")
                return False

            if not self.client.is_connected():
                logger.info("🔄 重新连接Telegram...")
                await self.client.connect()
                logger.info("✅ Telegram重连成功")
                self.connection_retries = 0
                return True
            else:
                return True

        except Exception as e:
            logger.error(f"❌ 确保连接失败: {e}")
            self.connection_retries += 1
            return False

    def set_feishu_notifier(self, feishu_notifier):
        """设置飞书通知器"""
        self.feishu_notifier = feishu_notifier

    async def _send_error_notification(self, error_type: str, error_message: str, details: str = ""):
        """发送错误通知到飞书"""
        if not self.feishu_notifier:
            return

        try:
            from datetime import datetime

            notification = f"""🚨 Telegram监控系统异常

📱 **监控系统**: Telegram长连接监控
⚠️ **异常类型**: {error_type}
❌ **错误信息**: {error_message}
📊 **详细信息**: {details}
🕐 **发生时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔧 **系统状态**:
- 监控频道: {', '.join(self.channels)}
- 重试次数: {self.connection_retries}/{self.max_retries}
- 运行状态: {'运行中' if self.running else '已停止'}

💡 **处理建议**:
- 检查网络连接是否正常
- 确认Telegram API配置正确
- 查看系统日志获取详细信息"""

            await self.feishu_notifier.send_message(notification)
            logger.info("✅ 错误通知已发送到飞书")

        except Exception as e:
            logger.error(f"发送飞书错误通知失败: {e}")

    async def _send_recovery_notification(self, recovery_type: str, details: str = ""):
        """发送恢复通知到飞书"""
        if not self.feishu_notifier:
            return

        try:
            from datetime import datetime

            notification = f"""✅ Telegram监控系统恢复

📱 **监控系统**: Telegram长连接监控
🔄 **恢复类型**: {recovery_type}
📊 **详细信息**: {details}
🕐 **恢复时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔧 **系统状态**:
- 监控频道: {', '.join(self.channels)}
- 连接状态: 正常
- 运行模式: {recovery_type}

🎉 **系统已恢复正常运行！**"""

            await self.feishu_notifier.send_message(notification)
            logger.info("✅ 恢复通知已发送到飞书")

        except Exception as e:
            logger.error(f"发送飞书恢复通知失败: {e}")
        
    async def get_historical_messages(self, days: int = 90) -> List[TelegramMessage]:
        """获取历史消息（用于历史分析）"""
        if not self.client:
            raise RuntimeError("Telegram客户端未初始化")
            
        try:
            entity = await self.get_channel_entity()
            if not entity:
                return []
                
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            messages = []
            logger.info(f"开始获取 {days} 天的历史消息...")
            
            async for message in self.client.iter_messages(
                entity, 
                offset_date=end_date,
                reverse=True
            ):
                if message.date < start_date:
                    break
                    
                if message.text:
                    telegram_msg = TelegramMessage(
                        id=message.id,
                        text=message.text,
                        date=message.date,
                        channel=self.channel,
                        sender=message.sender_id
                    )
                    messages.append(telegram_msg)
                    
            logger.info(f"获取到 {len(messages)} 条历史消息")
            return messages
            
        except Exception as e:
            logger.error(f"获取历史消息失败: {e}")
            return []
