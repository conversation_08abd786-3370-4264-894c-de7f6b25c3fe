# 固定策略配置示例 - 包含飞书通知
# 所有交易所使用相同的固定策略，无优先级差异

# 固定策略配置
fixed_strategy:
  enabled: true
  amount: 100  # 固定金额 100 USDT
  leverage: 10  # 10倍杠杆
  position_mode: "isolated"  # 逐仓模式
  first_close_time: 56  # 56秒后平仓80%
  first_close_percentage: 80  # 平仓80%
  monitor_duration: 300  # 5分钟监控期
  drawdown_threshold: 20  # 20%回撤阈值

# 币安交易配置
trading:
  binance:
    api_key: ""  # 请填入您的API Key
    api_secret: ""  # 请填入您的API Secret
    testnet: true  # 测试网模式，生产环境改为false
    base_url: "https://testnet.binancefuture.com"  # 测试网地址
    recv_window: 5000
    timeout: 30
    enabled: true

# Telegram监控配置
monitoring:
  telegram:
    api_id: ""  # 请填入您的API ID
    api_hash: ""  # 请填入您的API Hash
    phone: ""  # 请填入您的手机号
    channels:
      - "@BWEnews"
      - "@binance_announcements"
    # 精确的上币关键词 - 按交易所分类
    keywords:
      # Upbit关键词
      - "UPBIT LISTING:"
      - "UPBIT LISTING"
      - "Upbit 上新:"
      - "Upbit 上新: [交易]"
      - "[거래]"
      - "KRW 마켓"
      # Binance关键词
      - "Binance Will Add"
      # Coinbase关键词
      - "COINBASE LISTING:"
      - "COINBASE LISTING"
      - "COINBASE 上新"
      # Bithumb关键词
      - "Bithumb Listing:"
      - "Bithumb Listing"
      - "Bithumb상장:"
      - "Bithumb上新:"
      - "[마켓 추가]"
      - "마켓 추가"
      - "빗썸"
    # 排除关键词
    exclude_keywords:
      - "维护"
      - "暂停"
      - "停止"
      - "maintenance"
      - "suspend"
      - "delisting"
      - "下架"
      - "delist"
      - "暂停交易"
      - "停止交易"
      - "Token Swap"
      - "token swap"
      - "Rebranding"
      - "rebranding"
      - "Token Swap and Rebranding"

# 飞书通知配置
notifications:
  feishu:
    webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url"
    webhook_sign: ""  # 可选，用于签名验证
    enabled: true
    mention_all: false
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    to_emails: []
  telegram_bot:
    enabled: false
    token: ""
    chat_id: ""

# 风险控制配置 - 固定策略专用
risk_control:
  signal_cooldown: 300  # 信号冷却时间 5分钟
  max_errors: 10  # 最大错误次数
  error_window: 300  # 错误时间窗口 5分钟
  blacklist: []  # 黑名单代币

# 统计配置
statistics:
  enabled: true
  cleanup_interval: 3600  # 每小时清理一次
  max_history_days: 7  # 保留7天历史

# 日志配置
logging:
  level: "INFO"
  file: "trading_bot.log"
  max_size: "10MB"
  backup_count: 5
  retention: "30 days"
  rotation: "1 day"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"

# 系统配置
system:
  auto_restart: true
  backup_config: true
  health_check_interval: 60
  max_memory_mb: 1024
  timezone: "Asia/Shanghai"

# Web界面配置
web:
  host: "0.0.0.0"
  port: 8081
  debug: false
  cors_origins: ["*"]
  max_connections: 100
  session_timeout: 3600
