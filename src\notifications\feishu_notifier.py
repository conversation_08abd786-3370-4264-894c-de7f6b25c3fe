"""
飞书通知系统
实现信号分级、交易统计、开仓/平仓/盈利通知
"""
import json
import time
import hmac
import hashlib
import base64
from datetime import datetime
from typing import Dict, Any, Optional, List
from loguru import logger
import requests

from ..models import TradingSignal, TradingOrder, SignalSource


class SignalLevel:
    """信号等级 - 固定策略统一处理"""
    FIXED = "🎯 FIXED"         # 固定策略统一等级


class FeishuNotifier:
    """飞书通知器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.webhook_url = config.get('webhook_url', '')
        self.webhook_sign = config.get('webhook_sign', '')
        self.enabled = config.get('enabled', False) and bool(self.webhook_url)
        
        # 固定策略 - 所有交易所统一处理
        self.fixed_strategy_config = {
            'level': SignalLevel.FIXED,
            'emoji': '🎯',
            'name': '固定策略'
        }
        
        if self.enabled:
            logger.info("飞书通知器初始化完成")
        else:
            logger.warning("飞书通知器未启用或配置不完整")
    
    def _generate_sign(self, timestamp: int) -> str:
        """生成签名"""
        if not self.webhook_sign:
            return ""
            
        string_to_sign = f"{timestamp}\n{self.webhook_sign}"
        hmac_code = hmac.new(
            string_to_sign.encode("utf-8"), 
            digestmod=hashlib.sha256
        ).digest()
        sign = base64.b64encode(hmac_code).decode('utf-8')
        return sign
    
    def _send_message(self, content: Dict[str, Any]) -> bool:
        """发送消息到飞书"""
        if not self.enabled:
            return False
            
        try:
            timestamp = int(time.time())
            sign = self._generate_sign(timestamp)
            
            payload = {
                "timestamp": str(timestamp),
                "sign": sign,
                **content
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    logger.debug("飞书消息发送成功")
                    return True
                else:
                    logger.error(f"飞书消息发送失败: {result}")
                    return False
            else:
                logger.error(f"飞书API请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"发送飞书消息异常: {e}")
            return False
    
    def get_signal_level(self, signal: TradingSignal) -> Dict[str, Any]:
        """获取信号等级 - 固定策略统一处理"""
        # 固定策略：所有信号使用相同等级
        return self.fixed_strategy_config
    
    def send_signal_notification(self, signal: TradingSignal) -> bool:
        """发送信号通知"""
        if not self.enabled:
            return False
            
        try:
            level_info = self.get_signal_level(signal)
            level = level_info['level']
            emoji = level_info['emoji']
            
            # 构建消息内容
            listing_exchange_name = level_info.get('name', signal.exchange.upper())
            title = f"{emoji} {listing_exchange_name}上新币 - {level}"

            # 提取代币信息
            tokens_text = ", ".join([token.symbol for token in signal.tokens])
            market_cap = signal.metadata.get('market_cap') if signal.metadata else None
            market_cap_text = f"\n💰 市值: ${market_cap:,.0f}" if market_cap else ""

            # 信号来源信息
            signal_source_text = f"来源: {signal.source.value.upper()}"
            
            content = {
                "msg_type": "interactive",
                "card": {
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**{title}**",
                                "tag": "lark_md"
                            }
                        },
                        {
                            "tag": "hr"
                        },
                        {
                            "tag": "div",
                            "fields": [
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**代币符号**\n{tokens_text}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**上币交易所**\n{listing_exchange_name}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**置信度**\n{signal.confidence:.1%}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**时间**\n{signal.timestamp.strftime('%H:%M:%S')}",
                                        "tag": "lark_md"
                                    }
                                }
                            ]
                        },
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**信号内容**\n{signal.content[:200]}{'...' if len(signal.content) > 200 else ''}{market_cap_text}\n\n**{signal_source_text}**",
                                "tag": "lark_md"
                            }
                        }
                    ],
                    "header": {
                        "title": {
                            "content": f"{emoji} {listing_exchange_name}上新币信号",
                            "tag": "plain_text"
                        },
                        "template": "blue"
                    }
                }
            }
            
            return self._send_message(content)
            
        except Exception as e:
            logger.error(f"发送信号通知异常: {e}")
            return False
    
    def send_trade_notification(self, order: TradingOrder, action: str = "开仓") -> bool:
        """发送交易通知"""
        if not self.enabled:
            return False
            
        try:
            # 根据动作选择颜色和图标
            if action == "开仓":
                color = "green"
                icon = "📈"
            elif action == "平仓":
                color = "red" 
                icon = "📉"
            elif action == "止损":
                color = "orange"
                icon = "⚠️"
            else:
                color = "blue"
                icon = "📊"
            
            title = f"{icon} {action}通知"
            
            # 计算预期止损价格
            if order.filled_price and action == "开仓":
                stop_loss_price = order.filled_price * (1 - order.stop_loss_percent / 100)
                stop_loss_text = f"**止损价格**\n${stop_loss_price:.6f}"
            else:
                stop_loss_text = "**止损价格**\n待设置"
            
            content = {
                "msg_type": "interactive",
                "card": {
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**{title}**",
                                "tag": "lark_md"
                            }
                        },
                        {
                            "tag": "hr"
                        },
                        {
                            "tag": "div",
                            "fields": [
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**交易对**\n{order.symbol}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**方向**\n{order.side.value}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**开仓价格**\n${order.filled_price:.6f}" if order.filled_price else "**开仓价格**\n市价",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**杠杆倍数**\n{order.leverage}x",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**开单金额**\n${order.amount:.2f}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": stop_loss_text,
                                        "tag": "lark_md"
                                    }
                                }
                            ]
                        },
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**订单ID**: {order.order_id}\n**时间**: {order.created_at.strftime('%Y-%m-%d %H:%M:%S')}",
                                "tag": "lark_md"
                            }
                        }
                    ],
                    "header": {
                        "title": {
                            "content": f"{icon} 交易{action}",
                            "tag": "plain_text"
                        },
                        "template": color
                    }
                }
            }
            
            return self._send_message(content)

        except Exception as e:
            logger.error(f"发送交易通知异常: {e}")
            return False

    def send_profit_notification(self, order: TradingOrder, current_price: float, profit: float, profit_percent: float) -> bool:
        """发送盈利通知"""
        if not self.enabled:
            return False

        try:
            # 根据盈亏情况选择颜色和图标
            if profit > 0:
                color = "green"
                icon = "💰"
                status = "盈利"
            else:
                color = "red"
                icon = "💸"
                status = "亏损"

            title = f"{icon} {status}更新"

            content = {
                "msg_type": "interactive",
                "card": {
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**{title}**",
                                "tag": "lark_md"
                            }
                        },
                        {
                            "tag": "hr"
                        },
                        {
                            "tag": "div",
                            "fields": [
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**交易对**\n{order.symbol}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**开仓价格**\n${order.filled_price:.6f}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**当前价格**\n${current_price:.6f}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**{status}金额**\n${profit:+.2f}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**{status}比例**\n{profit_percent:+.2f}%",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**杠杆倍数**\n{order.leverage}x",
                                        "tag": "lark_md"
                                    }
                                }
                            ]
                        }
                    ],
                    "header": {
                        "title": {
                            "content": f"{icon} {status}跟踪",
                            "tag": "plain_text"
                        },
                        "template": color
                    }
                }
            }

            return self._send_message(content)

        except Exception as e:
            logger.error(f"发送盈利通知异常: {e}")
            return False

    def send_statistics_notification(self, stats: Dict[str, Any]) -> bool:
        """发送统计通知"""
        if not self.enabled:
            return False

        try:
            total_trades = stats.get('total_trades', 0)
            successful_trades = stats.get('successful_trades', 0)
            failed_trades = stats.get('failed_trades', 0)
            total_profit = stats.get('total_profit', 0)
            win_rate = stats.get('win_rate', 0)

            # 根据总盈利选择颜色
            if total_profit > 0:
                color = "green"
                icon = "📊"
            elif total_profit < 0:
                color = "red"
                icon = "📉"
            else:
                color = "blue"
                icon = "📈"

            content = {
                "msg_type": "interactive",
                "card": {
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**{icon} 交易统计报告**",
                                "tag": "lark_md"
                            }
                        },
                        {
                            "tag": "hr"
                        },
                        {
                            "tag": "div",
                            "fields": [
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**总交易次数**\n{total_trades}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**成功次数**\n{successful_trades}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**失败次数**\n{failed_trades}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": True,
                                    "text": {
                                        "content": f"**胜率**\n{win_rate:.1%}",
                                        "tag": "lark_md"
                                    }
                                },
                                {
                                    "is_short": False,
                                    "text": {
                                        "content": f"**累积盈利**\n${total_profit:+.2f}",
                                        "tag": "lark_md"
                                    }
                                }
                            ]
                        },
                        {
                            "tag": "div",
                            "text": {
                                "content": f"**报告时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                                "tag": "lark_md"
                            }
                        }
                    ],
                    "header": {
                        "title": {
                            "content": f"{icon} 交易统计",
                            "tag": "plain_text"
                        },
                        "template": color
                    }
                }
            }

            return self._send_message(content)

        except Exception as e:
            logger.error(f"发送统计通知异常: {e}")
            return False
