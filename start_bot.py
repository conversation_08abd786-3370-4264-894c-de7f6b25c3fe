#!/usr/bin/env python3
"""
智能交易机器人启动脚本
提供多种启动模式和控制选项
"""
import asyncio
import argparse
import sys
import os
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import TradingBot


def setup_arguments():
    """设置命令行参数"""
    parser = argparse.ArgumentParser(
        description="智能交易机器人控制系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start_bot.py                    # 启动机器人（默认模式）
  python start_bot.py --testnet          # 测试网模式
  python start_bot.py --web-only         # 仅启动Web界面
  python start_bot.py --config custom.yaml  # 使用自定义配置
  python start_bot.py --status           # 检查系统状态
  python start_bot.py --stop             # 停止运行中的机器人
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    
    parser.add_argument(
        '--testnet', '-t',
        action='store_true',
        help='使用测试网模式'
    )
    
    parser.add_argument(
        '--web-only', '-w',
        action='store_true',
        help='仅启动Web管理界面'
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=8080,
        help='Web界面端口 (默认: 8080)'
    )
    
    parser.add_argument(
        '--host',
        default='0.0.0.0',
        help='Web界面主机地址 (默认: 0.0.0.0)'
    )
    
    parser.add_argument(
        '--status', '-s',
        action='store_true',
        help='检查系统状态'
    )
    
    parser.add_argument(
        '--stop',
        action='store_true',
        help='停止运行中的机器人'
    )
    
    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    return parser


async def check_system_status():
    """检查系统状态"""
    try:
        # 检查是否有运行中的实例
        pid_file = "trading_bot.pid"
        if os.path.exists(pid_file):
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            try:
                os.kill(pid, 0)  # 检查进程是否存在
                print(f"✅ 机器人正在运行 (PID: {pid})")
                return True
            except OSError:
                print("❌ PID文件存在但进程不存在，清理PID文件")
                os.remove(pid_file)
                
        print("⭕ 机器人未运行")
        return False
        
    except Exception as e:
        print(f"❌ 检查状态异常: {e}")
        return False


async def stop_system():
    """停止系统"""
    try:
        pid_file = "trading_bot.pid"
        if not os.path.exists(pid_file):
            print("⭕ 没有找到运行中的机器人")
            return
            
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())
            
        try:
            import signal
            os.kill(pid, signal.SIGTERM)
            print(f"✅ 已发送停止信号给进程 {pid}")
            
            # 等待进程停止
            import time
            for i in range(10):
                try:
                    os.kill(pid, 0)
                    time.sleep(1)
                    print(f"⏳ 等待进程停止... ({i+1}/10)")
                except OSError:
                    print("✅ 机器人已停止")
                    os.remove(pid_file)
                    return
                    
            print("⚠️ 进程可能未完全停止，请手动检查")
            
        except OSError:
            print("❌ 进程不存在，清理PID文件")
            os.remove(pid_file)
            
    except Exception as e:
        print(f"❌ 停止系统异常: {e}")


async def start_web_only(host: str, port: int):
    """仅启动Web界面"""
    try:
        print(f"🌐 启动Web管理界面: http://{host}:{port}")
        
        # 这里可以启动一个简化的Web服务器
        # 暂时显示提示信息
        print("⚠️ Web-only模式需要完整实现")
        print("💡 建议使用完整模式启动机器人")
        
    except Exception as e:
        print(f"❌ 启动Web界面异常: {e}")


async def main():
    """主函数"""
    parser = setup_arguments()
    args = parser.parse_args()
    
    # 设置日志级别
    logger.remove()
    logger.add(
        sys.stderr,
        level=args.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 处理特殊命令
    if args.status:
        await check_system_status()
        return
        
    if args.stop:
        await stop_system()
        return
        
    if args.web_only:
        await start_web_only(args.host, args.port)
        return
        
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        print("💡 请先创建配置文件，可以参考 config.yaml 或 config_smart_strategy.yaml")
        return
        
    # 检查是否已有运行中的实例
    if await check_system_status():
        print("⚠️ 机器人已在运行中")
        response = input("是否要停止当前实例并重新启动? (y/N): ")
        if response.lower() == 'y':
            await stop_system()
            await asyncio.sleep(2)
        else:
            return
            
    try:
        print("🚀 启动智能交易机器人...")
        print(f"📋 配置文件: {args.config}")
        print(f"🌐 Web界面: http://{args.host}:{args.port}")
        
        if args.testnet:
            print("🧪 测试网模式: 启用")
            os.environ['BINANCE_TESTNET'] = 'true'
            
        if args.debug:
            print("🐛 调试模式: 启用")
            
        # 保存PID
        pid_file = "trading_bot.pid"
        with open(pid_file, 'w') as f:
            f.write(str(os.getpid()))
            
        # 创建并启动机器人
        bot = TradingBot()
        
        # 设置Web配置
        if not hasattr(bot, 'config'):
            bot.config = {}
        if 'web' not in bot.config:
            bot.config['web'] = {}
        bot.config['web']['host'] = args.host
        bot.config['web']['port'] = args.port
        bot.config['web']['enabled'] = True
        
        await bot.initialize()
        await bot.start()
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        logger.exception("启动异常详情")
    finally:
        # 清理PID文件
        pid_file = "trading_bot.pid"
        if os.path.exists(pid_file):
            os.remove(pid_file)
        print("👋 程序退出")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)
