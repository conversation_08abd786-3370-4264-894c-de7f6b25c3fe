# 🐳 智能交易机器人 Docker 部署指南

## 📋 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB RAM
- 至少 5GB 磁盘空间

## 🚀 快速启动

### 1. 准备配置文件

确保以下文件存在：
- `config.yaml` - 主配置文件
- `.env` - 环境变量文件

### 2. 配置API密钥

编辑 `.env` 文件：
```bash
# Binance API配置
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here

# Telegram配置
TELEGRAM_API_ID=26145597
TELEGRAM_API_HASH=859206f58db62ec957089a7e9ff11d38
TELEGRAM_PHONE=your_phone_number
```

### 3. 启动服务

```bash
# 方式1: 使用启动脚本（推荐）
chmod +x docker-start.sh
./docker-start.sh

# 方式2: 直接使用docker-compose
docker-compose up -d
```

## 📊 管理命令

```bash
# 查看服务状态
./docker-start.sh status
# 或
docker-compose ps

# 查看日志
./docker-start.sh logs
# 或
docker-compose logs -f goldbot

# 重启服务
./docker-start.sh restart
# 或
docker-compose restart

# 停止服务
./docker-start.sh stop
# 或
docker-compose down

# 清理所有数据
./docker-start.sh clean
```

## 🌐 访问界面

- **Web管理界面**: http://localhost:8080
- **健康检查**: http://localhost:8080/api/status

## 📁 数据持久化

Docker容器使用以下卷来持久化数据：

- `goldbot-data`: 数据库文件
- `goldbot-logs`: 日志文件
- `goldbot-cache`: 缓存文件
- `goldbot-redis`: Redis数据（可选）

## 🔧 配置说明

### Dockerfile
- 基于 Python 3.11-slim
- 自动安装依赖
- 设置时区为 Asia/Shanghai
- 暴露端口 8080

### docker-compose.yml
- 主服务: goldbot
- 可选服务: Redis缓存
- 自动重启策略
- 健康检查

## 🛠️ 故障排除

### 1. 容器启动失败
```bash
# 查看详细日志
docker-compose logs goldbot

# 检查配置文件
docker-compose config
```

### 2. API连接问题
```bash
# 进入容器检查
docker-compose exec goldbot bash

# 检查网络连接
docker-compose exec goldbot curl -I https://fapi.binance.com
```

### 3. 权限问题
```bash
# 重新构建镜像
docker-compose build --no-cache

# 清理并重启
docker-compose down -v
docker-compose up -d
```

## 📈 监控和维护

### 日志管理
```bash
# 实时查看日志
docker-compose logs -f goldbot

# 查看最近100行日志
docker-compose logs --tail=100 goldbot
```

### 资源监控
```bash
# 查看容器资源使用
docker stats goldbot-trading

# 查看容器详细信息
docker inspect goldbot-trading
```

### 备份数据
```bash
# 备份数据卷
docker run --rm -v goldbot-data:/data -v $(pwd):/backup alpine tar czf /backup/goldbot-data-backup.tar.gz -C /data .

# 恢复数据
docker run --rm -v goldbot-data:/data -v $(pwd):/backup alpine tar xzf /backup/goldbot-data-backup.tar.gz -C /data
```

## 🔄 更新部署

```bash
# 1. 停止服务
docker-compose down

# 2. 拉取最新代码
git pull

# 3. 重新构建镜像
docker-compose build --no-cache

# 4. 启动服务
docker-compose up -d
```

## 🌍 生产环境部署

### 1. 使用外部数据库
修改 `docker-compose.yml`，添加PostgreSQL或MySQL服务。

### 2. 负载均衡
使用Nginx反向代理多个容器实例。

### 3. 监控告警
集成Prometheus + Grafana监控。

### 4. 日志收集
使用ELK Stack收集和分析日志。

## 🔐 安全建议

1. **API密钥安全**
   - 使用Docker Secrets管理敏感信息
   - 定期轮换API密钥

2. **网络安全**
   - 使用自定义网络
   - 限制端口暴露

3. **容器安全**
   - 定期更新基础镜像
   - 使用非root用户运行

## 📞 技术支持

如遇问题，请检查：
1. Docker和Docker Compose版本
2. 配置文件格式
3. API密钥权限
4. 网络连接状态

更多帮助请查看项目文档或提交Issue。
