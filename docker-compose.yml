services:
  goldbot:
    build: .
    container_name: goldbot-trading
    restart: unless-stopped
    ports:
      - "8080:8081"
      - "8000:8081"
    volumes:
      # 配置文件挂载
      - ./config.yaml:/app/config.yaml:ro
      - ./.env:/app/.env:ro
      # Telegram会话文件挂载
      - ./telegram_session.session:/app/telegram_session.session
      - ./goldbot_session.session:/app/goldbot_session.session
      # 数据持久化
      - goldbot-data:/app/data
      - goldbot-logs:/app/logs
      - goldbot-cache:/app/cache
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Shanghai
    networks:
      - goldbot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: goldbot-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - goldbot-redis:/data
    networks:
      - goldbot-network
    command: redis-server --appendonly yes

volumes:
  goldbot-data:
    driver: local
  goldbot-logs:
    driver: local
  goldbot-cache:
    driver: local
  goldbot-redis:
    driver: local

networks:
  goldbot-network:
    driver: bridge
