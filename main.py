#!/usr/bin/env python3
"""
智能交易机器人主程序
集成系统控制管理和Web界面
"""
import asyncio
import signal
import sys
from typing import Dict, Any, Optional
from loguru import logger

from src.utils import load_config, load_env_config, setup_logger
from src.monitors.telegram_monitor import TelegramMonitor
from src.core.fixed_signal_processor import FixedSignalProcessor
from src.core.system_controller import SystemController
from src.web.web_server import WebServer
from src.trading.fixed_strategy import initialize_fixed_strategy
from src.trading.contract_manager import ContractManager
from binance.client import Client as BinanceClient


class TradingBot:
    """智能交易机器人主类"""
    
    def __init__(self):
        self.config: Dict[str, Any] = {}
        self.system_controller: Optional[SystemController] = None
        self.web_server: Optional[WebServer] = None

        self.telegram_monitor: Optional[TelegramMonitor] = None
        self.signal_processor: Optional[FixedSignalProcessor] = None
        self.binance_client: Optional[BinanceClient] = None
        self.contract_manager: Optional[ContractManager] = None
        self.fixed_strategy = None
        self.running = False
        
    async def initialize(self):
        """初始化机器人"""
        try:
            logger.info("🚀 开始初始化智能交易机器人...")
            
            # 1. 加载配置
            logger.info("📋 加载配置文件...")
            self.config = load_config()
            env_config = load_env_config()
            
            # 2. 合并环境变量配置
            self._merge_config(self.config, env_config)
            
            # 3. 设置日志
            log_config = self.config.get('notifications', {}).get('logging', {})
            setup_logger(
                log_file=log_config.get('file', 'trading_bot.log'),
                level=log_config.get('level', 'INFO')
            )
            
            # 4. 初始化系统控制器
            logger.info("🎛️ 初始化系统控制器...")
            self.system_controller = SystemController(self.config)
            
            # 5. 初始化固定策略信号处理器
            logger.info("🧠 初始化固定策略信号处理器...")
            self.signal_processor = FixedSignalProcessor(self.config)
            
            # 6. 初始化交易组件
            logger.info("💰 初始化交易组件...")
            await self._initialize_trading_components()

            # 7. 初始化监控器
            logger.info("👁️ 初始化监控器...")
            await self._initialize_monitors()

            # 8. 注册系统组件
            logger.info("🔗 注册系统组件...")
            self.system_controller.register_component('signal_processor', self.signal_processor)

            if self.telegram_monitor:
                self.system_controller.register_component('telegram_monitor', self.telegram_monitor)

            if self.binance_client:
                self.system_controller.register_component('binance_client', self.binance_client)

            if self.contract_manager:
                self.system_controller.register_component('contract_manager', self.contract_manager)

            if self.fixed_strategy:
                self.system_controller.register_component('fixed_strategy', self.fixed_strategy)

            # 9. 初始化Web服务器
            web_config = self.config.get('web', {})
            if web_config.get('enabled', True):
                logger.info("🌐 初始化Web管理界面...")
                self.web_server = WebServer(self.system_controller, web_config)
                
            logger.info("✅ 智能交易机器人初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 初始化失败: {e}")
            raise
            
    def _merge_config(self, base_config: Dict[str, Any], env_config: Dict[str, Any]):
        """合并配置"""
        try:
            # 合并币安配置
            if 'trading' not in base_config:
                base_config['trading'] = {}
            if 'binance' not in base_config['trading']:
                base_config['trading']['binance'] = {}
            base_config['trading']['binance'].update(env_config.get('binance', {}))
            
            # 合并Telegram配置
            if 'monitoring' not in base_config:
                base_config['monitoring'] = {}
            if 'telegram' not in base_config['monitoring']:
                base_config['monitoring']['telegram'] = {}
            base_config['monitoring']['telegram'].update(env_config.get('telegram', {}))
            
            # 合并飞书配置
            if 'notifications' not in base_config:
                base_config['notifications'] = {}
            if 'feishu' not in base_config['notifications']:
                base_config['notifications']['feishu'] = {}
            base_config['notifications']['feishu'].update(env_config.get('feishu', {}))
            
            logger.debug("配置合并完成")
            
        except Exception as e:
            logger.error(f"合并配置异常: {e}")

    async def _initialize_trading_components(self):
        """初始化交易组件"""
        try:
            # 初始化币安客户端
            binance_config = self.config.get('trading', {}).get('binance', {})
            if binance_config.get('api_key') and binance_config.get('api_secret'):
                self.binance_client = BinanceClient(
                    api_key=binance_config['api_key'],
                    api_secret=binance_config['api_secret'],
                    testnet=binance_config.get('testnet', False)
                )
                logger.info("✓ 币安客户端初始化完成")

                # 初始化合约管理器
                self.contract_manager = ContractManager(self.binance_client)
                await self.contract_manager.initialize(self.binance_client)
                logger.info("✓ 合约管理器初始化完成")

                # 初始化固定策略
                self.fixed_strategy = initialize_fixed_strategy(
                    self.binance_client,
                    self.contract_manager
                )
                logger.info("✓ 固定交易策略初始化完成")

            else:
                logger.warning("⚠️ 币安配置不完整，跳过交易组件初始化")

        except Exception as e:
            logger.error(f"初始化交易组件异常: {e}")
            raise

    async def _initialize_monitors(self):
        """初始化监控器"""
        try:
            # 初始化Telegram监控器（如果配置了）
            telegram_config = self.config.get('monitoring', {}).get('telegram', {})
            if telegram_config.get('api_id') and telegram_config.get('api_hash'):
                self.telegram_monitor = TelegramMonitor(telegram_config)
                logger.info("✓ Telegram监控器初始化完成")
            else:
                logger.warning("⚠️ Telegram配置不完整，跳过Telegram监控器初始化")
                
        except Exception as e:
            logger.error(f"初始化监控器异常: {e}")
            raise
            
    async def start(self):
        """启动机器人"""
        try:
            if self.running:
                logger.warning("机器人已在运行中")
                return
                
            logger.info("🚀 启动智能交易机器人...")
            self.running = True
            
            # 启动系统控制器
            if self.system_controller:
                success = await self.system_controller.start_system()
                if not success:
                    logger.error("系统控制器启动失败")
                    return
                    
            # 启动Web服务器（如果配置了）
            if self.web_server:
                web_config = self.config.get('web', {})
                host = web_config.get('host', '0.0.0.0')
                port = web_config.get('port', 8080)
                
                # 在后台启动Web服务器
                web_task = asyncio.create_task(
                    self.web_server.start_server(host, port)
                )
                logger.info(f"🌐 Web管理界面已启动: http://{host}:{port}")
                
            logger.info("✅ 智能交易机器人启动成功")
            logger.info("📊 系统状态: 运行中")
            logger.info("🔔 通知系统: 已激活")
            logger.info("🎯 策略系统: 固定策略已启用")
            
            # 等待系统运行
            if self.system_controller:
                while self.system_controller.is_running():
                    await asyncio.sleep(1)
                    
        except Exception as e:
            logger.error(f"启动机器人异常: {e}")
            await self.stop()
            
    async def stop(self):
        """停止机器人"""
        try:
            if not self.running:
                logger.info("机器人已停止")
                return
                
            logger.info("🛑 停止智能交易机器人...")
            self.running = False
            
            # 停止系统控制器
            if self.system_controller:
                await self.system_controller.stop_system("用户请求停止")
                
            # 停止Web服务器
            if self.web_server:
                await self.web_server.stop_server()
                
            logger.info("✅ 智能交易机器人已安全停止")
            
        except Exception as e:
            logger.error(f"停止机器人异常: {e}")
            
    async def restart(self):
        """重启机器人"""
        logger.info("🔄 重启智能交易机器人...")
        await self.stop()
        await asyncio.sleep(2)
        await self.start()
        
    def get_status(self) -> Dict[str, Any]:
        """获取机器人状态"""
        try:
            if self.system_controller:
                health = self.system_controller.get_system_health()
                return {
                    'running': self.running,
                    'status': health.status.value,
                    'uptime': health.uptime_seconds,
                    'active_monitors': health.active_monitors,
                    'active_positions': health.active_positions,
                    'total_signals': health.total_signals,
                    'total_trades': health.total_trades,
                    'error_count': health.error_count
                }
            else:
                return {
                    'running': self.running,
                    'status': 'unknown',
                    'message': '系统控制器未初始化'
                }
        except Exception as e:
            logger.error(f"获取状态异常: {e}")
            return {'running': False, 'status': 'error', 'error': str(e)}


async def main():
    """主函数"""
    bot = TradingBot()
    
    try:
        # 初始化机器人
        await bot.initialize()
        
        # 设置信号处理
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，准备退出...")
            asyncio.create_task(bot.stop())
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 启动机器人
        await bot.start()
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        await bot.stop()
    except Exception as e:
        logger.error(f"程序异常: {e}")
        await bot.stop()
        sys.exit(1)
    finally:
        logger.info("程序退出")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被中断")
    except Exception as e:
        logger.error(f"程序启动异常: {e}")
        sys.exit(1)
