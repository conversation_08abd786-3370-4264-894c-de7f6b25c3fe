# 🤖 智能交易机器人系统

一个基于人工智能的智能交易机器人，具备差异化策略、历史数据分析、长连接监控和完整的故障通知功能。

## ✨ 核心特性

- 🎯 **智能差异化策略** - 基于历史数据的科学配置
- 📊 **历史数据分析** - 深度分析各交易所上币效果  
- 🔗 **Telegram长连接监控** - 事件驱动的实时监控
- 🚨 **完整故障通知** - 飞书实时通知系统状态
- 🌐 **Web可视化管理** - 直观的监控和控制界面

## 🚀 快速开始

### 1. 启动系统
```bash
python start_bot.py
```

### 2. 访问Web界面
```
http://localhost:8080
```

### 3. 查看系统状态
```bash
python scripts/quick_status.py
```

## 📁 项目结构

```
goldbot/
├── 📚 docs/          # 完整文档
├── 🧪 tests/         # 测试套件
├── 💡 examples/      # 示例代码
├── 🛠️ scripts/       # 实用工具
├── ⚙️ configs/       # 配置示例
├── 🔧 src/           # 源代码
├── 📄 main.py        # 主程序
└── 📄 start_bot.py   # 启动脚本
```

## 📖 文档

- [📚 文档中心](docs/README.md) - 完整文档索引
- [⚙️ 配置说明](docs/guides/CONFIGURATION_SUMMARY.md) - 完整配置指南
- [🎯 高级功能](docs/guides/ADVANCED_FEATURES.md) - 系统功能详解
- [🎯 固定策略指南](FIXED_STRATEGY_GUIDE.md) - 固定策略使用指南

## 🎯 固定交易策略

**所有交易所使用相同的固定策略**：

| 参数 | 值 | 说明 |
|------|-----|------|
| **固定金额** | 100 USDT | 每次开仓金额 |
| **杠杆倍数** | 10x | 固定杠杆 |
| **仓位模式** | 逐仓 | 风险隔离 |
| **首次平仓** | 56秒后80% | 快速获利 |
| **剩余管理** | 智能平仓 | 回撤/时间控制 |

## 📱 监控配置

- **Telegram频道**: @BWEnews
- **监控关键词**: COINBASE LISTING, UPBIT LISTING, Bithumb上新等
- **飞书通知**: 实时推送交易信号和系统状态
- **故障通知**: 连接断开、恢复、故障转移等

## 🔧 系统要求

- Python 3.8+
- 网络连接 (访问Telegram API和交易所API)
- 配置文件 (config.yaml)
- 环境变量 (.env)

## 📞 技术支持

- 📖 查看 [文档中心](docs/README.md)
- 🧪 运行 [测试套件](tests/README.md)  
- 💡 参考 [示例代码](examples/README.md)
- 🛠️ 使用 [实用工具](scripts/README.md)

---

**这是一个企业级的智能交易机器人系统！** 🚀
#   g o l d c o i n 
 
 