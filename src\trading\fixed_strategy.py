#!/usr/bin/env python3
"""
固定交易策略
不再使用差异化策略，所有交易所都使用相同的固定策略
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class PositionStatus(Enum):
    """仓位状态"""
    OPENING = "opening"           # 开仓中
    OPENED = "opened"            # 已开仓
    PARTIAL_CLOSED = "partial_closed"  # 部分平仓
    CLOSED = "closed"            # 已平仓
    MANUAL = "manual"            # 手动管理

@dataclass
class FixedPosition:
    """固定策略仓位"""
    symbol: str
    exchange: str
    announcement_time: datetime
    open_time: datetime
    open_price: float
    quantity: float
    status: PositionStatus
    
    # 仓位管理
    initial_amount: float = 100.0  # 初始金额 100 USDT
    leverage: int = 10             # 10倍杠杆
    remaining_percentage: float = 100.0  # 剩余仓位百分比
    
    # 价格追踪
    peak_price: float = 0.0        # 峰值价格
    current_price: float = 0.0     # 当前价格
    
    # 平仓记录
    first_close_time: Optional[datetime] = None   # 80%平仓时间
    final_close_time: Optional[datetime] = None   # 最终平仓时间
    
    # 多交易所标记
    multiple_exchanges: bool = False  # 是否多个交易所上线

class FixedTradingStrategy:
    """固定交易策略"""
    
    def __init__(self, binance_client, contract_manager):
        self.binance_client = binance_client
        self.contract_manager = contract_manager
        self.active_positions: Dict[str, FixedPosition] = {}
        self.position_tasks: Dict[str, asyncio.Task] = {}
        
        # 策略参数
        self.FIXED_AMOUNT = 100.0      # 固定金额 100 USDT
        self.LEVERAGE = 10             # 10倍杠杆
        self.FIRST_CLOSE_TIME = 56     # 56秒后平仓80%
        self.FIRST_CLOSE_PERCENTAGE = 80  # 平仓80%
        self.MONITOR_DURATION = 300    # 5分钟监控期
        self.DRAWDOWN_THRESHOLD = 20   # 20%回撤阈值
        
        logger.info("🎯 固定交易策略初始化完成")
        logger.info(f"   固定金额: {self.FIXED_AMOUNT} USDT")
        logger.info(f"   杠杆倍数: {self.LEVERAGE}x")
        logger.info(f"   首次平仓: {self.FIRST_CLOSE_TIME}秒后平仓{self.FIRST_CLOSE_PERCENTAGE}%")
    
    async def process_listing_signal(self, symbol: str, exchange: str, announcement_time: datetime, 
                                   multiple_exchanges: bool = False) -> bool:
        """
        处理上币信号
        
        Args:
            symbol: 代币符号
            exchange: 交易所
            announcement_time: 公告时间(精确到秒)
            multiple_exchanges: 是否多个交易所上线同一代币
        
        Returns:
            bool: 是否成功开仓
        """
        try:
            logger.info(f"🔔 收到上币信号: {symbol} ({exchange}) - {announcement_time}")
            
            # 1. 检查币安合约交易对缓存
            contract_symbol = f"{symbol}USDT"
            
            if not self.contract_manager.is_symbol_available(contract_symbol):
                logger.warning(f"❌ {contract_symbol} 合约不存在，跳过交易")
                return False
            
            logger.info(f"✅ {contract_symbol} 合约存在，准备开仓")
            
            # 2. 立即开仓
            position = await self._open_position(symbol, exchange, announcement_time, multiple_exchanges)
            
            if position:
                # 3. 启动仓位管理任务
                task = asyncio.create_task(self._manage_position(position))
                self.position_tasks[symbol] = task
                
                logger.info(f"🚀 {symbol} 开仓成功，启动仓位管理")
                return True
            else:
                logger.error(f"❌ {symbol} 开仓失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 处理上币信号失败: {symbol} - {e}")
            return False
    
    async def _open_position(self, symbol: str, exchange: str, announcement_time: datetime,
                           multiple_exchanges: bool) -> Optional[FixedPosition]:
        """开仓"""
        try:
            contract_symbol = f"{symbol}USDT"
            
            # 获取当前价格
            ticker = await self.binance_client.get_symbol_ticker(symbol=contract_symbol)
            current_price = float(ticker['price'])
            
            # 计算开仓数量
            quantity = (self.FIXED_AMOUNT * self.LEVERAGE) / current_price
            
            # 设置杠杆
            await self.binance_client.futures_change_leverage(
                symbol=contract_symbol,
                leverage=self.LEVERAGE
            )
            
            # 设置逐仓模式
            await self.binance_client.futures_change_margin_type(
                symbol=contract_symbol,
                marginType='ISOLATED'
            )
            
            # 开多仓
            await self.binance_client.futures_create_order(
                symbol=contract_symbol,
                side='BUY',
                type='MARKET',
                quantity=quantity
            )
            
            # 创建仓位对象
            position = FixedPosition(
                symbol=symbol,
                exchange=exchange,
                announcement_time=announcement_time,
                open_time=datetime.now(),
                open_price=current_price,
                quantity=quantity,
                status=PositionStatus.OPENED,
                peak_price=current_price,
                current_price=current_price,
                multiple_exchanges=multiple_exchanges
            )
            
            self.active_positions[symbol] = position
            
            logger.info(f"✅ {symbol} 开仓成功:")
            logger.info(f"   价格: ${current_price:.6f}")
            logger.info(f"   数量: {quantity:.4f}")
            logger.info(f"   金额: {self.FIXED_AMOUNT} USDT")
            logger.info(f"   杠杆: {self.LEVERAGE}x")
            logger.info(f"   多交易所: {multiple_exchanges}")
            
            return position
            
        except Exception as e:
            logger.error(f"❌ 开仓失败: {symbol} - {e}")
            return None
    
    async def _manage_position(self, position: FixedPosition):
        """管理仓位"""
        try:
            logger.info(f"📊 开始管理仓位: {position.symbol}")
            
            # 等待56秒
            await asyncio.sleep(self.FIRST_CLOSE_TIME)
            
            # 平仓80%
            await self._close_partial_position(position, self.FIRST_CLOSE_PERCENTAGE)
            
            # 如果是多交易所上线，转为手动管理
            if position.multiple_exchanges:
                position.status = PositionStatus.MANUAL
                logger.info(f"🔄 {position.symbol} 多交易所上线，转为手动管理")
                return
            
            # 监控剩余20%仓位
            await self._monitor_remaining_position(position)
            
        except Exception as e:
            logger.error(f"❌ 仓位管理失败: {position.symbol} - {e}")
    
    async def _close_partial_position(self, position: FixedPosition, percentage: float):
        """部分平仓"""
        try:
            contract_symbol = f"{position.symbol}USDT"
            
            # 计算平仓数量
            close_quantity = position.quantity * (percentage / 100)
            
            # 平仓
            order = await self.binance_client.futures_create_order(
                symbol=contract_symbol,
                side='SELL',
                type='MARKET',
                quantity=close_quantity
            )
            
            # 更新仓位状态
            position.remaining_percentage = 100 - percentage
            position.status = PositionStatus.PARTIAL_CLOSED
            position.first_close_time = datetime.now()
            
            # 获取平仓价格
            close_price = float(order.get('avgPrice', position.current_price))
            profit_percentage = ((close_price - position.open_price) / position.open_price) * 100
            
            logger.info(f"✅ {position.symbol} 部分平仓 {percentage}%:")
            logger.info(f"   平仓价格: ${close_price:.6f}")
            logger.info(f"   收益率: {profit_percentage:.2f}%")
            logger.info(f"   剩余仓位: {position.remaining_percentage}%")
            
        except Exception as e:
            logger.error(f"❌ 部分平仓失败: {position.symbol} - {e}")
    
    async def _monitor_remaining_position(self, position: FixedPosition):
        """监控剩余仓位"""
        try:
            logger.info(f"👁️ 开始监控剩余仓位: {position.symbol}")
            
            contract_symbol = f"{position.symbol}USDT"
            monitor_end = position.announcement_time + timedelta(minutes=5)
            
            while datetime.now() < monitor_end:
                # 获取当前价格
                ticker = await self.binance_client.get_symbol_ticker(symbol=contract_symbol)
                current_price = float(ticker['price'])
                
                # 更新价格追踪
                position.current_price = current_price
                if current_price > position.peak_price:
                    position.peak_price = current_price
                
                # 检查是否从峰值回落超过20%
                if position.peak_price > 0:
                    drawdown = ((position.peak_price - current_price) / position.peak_price) * 100
                    
                    if drawdown > self.DRAWDOWN_THRESHOLD:
                        logger.info(f"📉 {position.symbol} 从峰值回落 {drawdown:.2f}% > {self.DRAWDOWN_THRESHOLD}%，立即平仓")
                        await self._close_final_position(position, "drawdown")
                        return
                
                # 等待1秒后继续监控
                await asyncio.sleep(1)
            
            # 5分钟到期，平仓剩余仓位
            logger.info(f"⏰ {position.symbol} 5分钟监控期结束，平仓剩余仓位")
            await self._close_final_position(position, "timeout")
            
        except Exception as e:
            logger.error(f"❌ 监控剩余仓位失败: {position.symbol} - {e}")
    
    async def _close_final_position(self, position: FixedPosition, reason: str):
        """最终平仓"""
        try:
            contract_symbol = f"{position.symbol}USDT"
            
            # 计算剩余数量
            remaining_quantity = position.quantity * (position.remaining_percentage / 100)
            
            # 平仓
            order = await self.binance_client.futures_create_order(
                symbol=contract_symbol,
                side='SELL',
                type='MARKET',
                quantity=remaining_quantity
            )
            
            # 更新仓位状态
            position.status = PositionStatus.CLOSED
            position.final_close_time = datetime.now()
            position.remaining_percentage = 0
            
            # 获取平仓价格
            close_price = float(order.get('avgPrice', position.current_price))
            total_profit = ((close_price - position.open_price) / position.open_price) * 100
            
            logger.info(f"✅ {position.symbol} 最终平仓 ({reason}):")
            logger.info(f"   平仓价格: ${close_price:.6f}")
            logger.info(f"   总收益率: {total_profit:.2f}%")
            logger.info(f"   峰值价格: ${position.peak_price:.6f}")
            
            # 从活跃仓位中移除
            if position.symbol in self.active_positions:
                del self.active_positions[position.symbol]
            
            # 取消管理任务
            if position.symbol in self.position_tasks:
                self.position_tasks[position.symbol].cancel()
                del self.position_tasks[position.symbol]
            
        except Exception as e:
            logger.error(f"❌ 最终平仓失败: {position.symbol} - {e}")
    
    def get_active_positions(self) -> List[FixedPosition]:
        """获取活跃仓位"""
        return list(self.active_positions.values())
    
    async def manual_close_position(self, symbol: str) -> bool:
        """手动平仓"""
        try:
            if symbol not in self.active_positions:
                logger.warning(f"❌ 未找到活跃仓位: {symbol}")
                return False
            
            position = self.active_positions[symbol]
            await self._close_final_position(position, "manual")
            
            logger.info(f"✅ {symbol} 手动平仓完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 手动平仓失败: {symbol} - {e}")
            return False

# 全局策略实例
fixed_strategy = None

def initialize_fixed_strategy(binance_client, contract_manager):
    """初始化固定策略"""
    global fixed_strategy
    fixed_strategy = FixedTradingStrategy(binance_client, contract_manager)
    return fixed_strategy

def get_fixed_strategy():
    """获取固定策略实例"""
    return fixed_strategy
